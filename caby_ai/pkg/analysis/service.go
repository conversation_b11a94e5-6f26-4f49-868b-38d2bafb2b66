package analysis

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url" // Import the url package
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"caby-ai/config"
	"caby-ai/pkg/models"
	"caby-ai/pkg/qdrant"
	"caby-ai/pkg/shadow"
	"caby-ai/pkg/vision"

	"github.com/grafov/m3u8"
	"gocv.io/x/gocv"
)

const (
	// Constants for static video detection
	minDurationForStaticCheck = 4.0 // Minimum original duration (seconds) to consider for static check
	maxKeyframesForStatic     = 10  // Maximum number of keyframes allowed for a video to be considered static (if duration >= minDurationForStaticCheck)

	// Constants for segment download retries
	maxDownloadRetries = 30              // Number of times to retry downloading a segment
	retryDelay         = 2 * time.Second // Delay between retries
)

// Service handles the core video analysis logic.
type Service struct {
	qdrantClient   *qdrant.QdrantClient
	cfg            *config.Config
	httpClient     *http.Client
	shadowService  *shadow.Service
	featuredClient *vision.FeaturedClient
}

// Cat name to ID mapping (temporary for first version)
var catNameToIDMapping = map[string]string{
	"小黑": "f3ce1b02b2c1d755421000",
	"小白": "f3ce1b02b40e9477c21000",
	"小花": "f3ce1b02b40ed223821000",
}

// KeyframeInfo holds metadata about an extracted keyframe
type KeyframeInfo struct {
	FrameNumber   int     `json:"frame_number"`
	Timestamp     float64 `json:"timestamp"`
	TimeStr       string  `json:"time_str"`
	Filename      string  `json:"filename"`
	TimeToNext    float64 `json:"time_to_next,omitempty"`
	TimeToNextStr string  `json:"time_to_next_str,omitempty"`
}

// KeyframeExtractionResult holds the results of keyframe extraction
type KeyframeExtractionResult struct {
	Keyframes         []KeyframeInfo
	TotalFrames       int
	ProcessedDuration float64 // Renamed TotalDuration to ProcessedDuration
	OriginalDuration  float64 // Added field for duration from M3U8
	FPS               float64
	TempDir           string // Path to the temporary directory holding keyframe images
}

// NewService creates a new analysis service.
func NewService(cfg *config.Config, qdrantClient *qdrant.QdrantClient) *Service {
	// 创建影子模式服务
	shadowService := shadow.NewService(qdrantClient, cfg)

	// 创建特征提取客户端
	featuredBaseURL := fmt.Sprintf("http://%s:%d", cfg.Vision.Host, cfg.Vision.Port)
	featuredClient := vision.NewFeaturedClient(featuredBaseURL, cfg.Vision.ApiKey)

	return &Service{
		cfg:            cfg,
		qdrantClient:   qdrantClient,
		httpClient:     &http.Client{Timeout: 30 * time.Second}, // Initialize HTTP client
		shadowService:  shadowService,
		featuredClient: featuredClient,
	}
}

// AnalyzeRecord performs the analysis for a given record.
func (s *Service) AnalyzeRecord(ctx context.Context, record *models.RecordShit) (*models.RecordAnalysis, error) {
	log.Printf("Starting analysis for video: %s (Device: %s, User: %s)", record.VideoID, record.DeviceID, record.UserID)
	startTime := time.Now()

	// 1. Get record information
	userID := record.UserID
	knownCatIDs := record.KnownCatIDs
	deviceID := record.DeviceID
	videoURL := record.VideoPath // Assuming this is always an HLS playlist URL

	log.Printf("Received UserID: %s, Known CatIDs: %v, DeviceID: %s", userID, knownCatIDs, deviceID)
	log.Printf("Received HLS VideoPath URL: %s", videoURL)
	log.Printf("Received Start Time: %s, End Time: %s", record.StartTime, record.EndTime)

	if deviceID == "" || videoURL == "" {
		return nil, fmt.Errorf("device ID or video path URL is empty for video %s", record.VideoID)
	}

	// 2. Create a temporary directory for processing
	tempBaseDir := filepath.Join(os.TempDir(), "caby_analysis")
	if err := os.MkdirAll(tempBaseDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create temp base directory: %w", err)
	}

	// Use MkdirTemp to create a temporary directory, not a file
	tempDirPath, errTemp := os.MkdirTemp(tempBaseDir, fmt.Sprintf("%s-*", record.VideoID))
	if errTemp != nil {
		return nil, fmt.Errorf("failed to create temporary directory: %w", errTemp)
	}

	// Ensure cleanup happens when we're done
	defer func() {
		log.Printf("Cleaning up temporary directory: %s", tempDirPath)
		if err := os.RemoveAll(tempDirPath); err != nil {
			log.Printf("Warning: Failed to remove temporary directory %s: %v", tempDirPath, err)
		}
	}()

	log.Printf("Created temporary directory for processing: %s", tempDirPath)

	// 3. Process video as HLS stream
	var extractionResult *KeyframeExtractionResult
	var err error
	keyframeThreshold := 15.0 // Default threshold for keyframe detection

	log.Printf("Processing as HLS stream: %s", videoURL)
	extractionResult, err = s.extractKeyframesFromHLS(ctx, videoURL, tempDirPath, keyframeThreshold)

	if err != nil {
		log.Printf("Keyframe extraction failed for %s: %v", record.VideoID, err)
		return nil, fmt.Errorf("keyframe extraction failed: %w", err)
	}

	if extractionResult == nil {
		log.Printf("Error: Keyframe extraction returned nil result but no error for %s", record.VideoID)
		return nil, fmt.Errorf("keyframe extraction returned nil result unexpectedly for video %s", record.VideoID)
	}

	log.Printf("Extracted %d keyframes from HLS stream (Original Duration: %.2fs, Processed Duration: %.2fs)",
		len(extractionResult.Keyframes), extractionResult.OriginalDuration, extractionResult.ProcessedDuration)

	// 4. Detect static/invalid videos
	isStatic := extractionResult.OriginalDuration >= minDurationForStaticCheck && len(extractionResult.Keyframes) < maxKeyframesForStatic
	if isStatic {
		log.Printf("Video %s appears to be mostly static (Original Duration: %.2fs >= %.2fs, Keyframes: %d < %d)",
			record.VideoID, extractionResult.OriginalDuration, minDurationForStaticCheck, len(extractionResult.Keyframes), maxKeyframesForStatic)

		// Create analysis result for static video
		analysisResult := &models.RecordAnalysis{
			VideoID:      record.VideoID,
			AnimalID:     "unknown", // Cannot identify animal in static video
			BehaviorType: "static_video",
			IsAbnormal:   false,
			AbnormalType: "",
			AbnormalProb: 0,
			AiResults: fmt.Sprintf(`{"status":"skipped","reason":"static video","original_duration":%.2f,"processed_duration":%.2f,"keyframes":%d,"start_time":"%s","end_time":"%s"}`,
				extractionResult.OriginalDuration, extractionResult.ProcessedDuration, len(extractionResult.Keyframes),
				record.StartTime, record.EndTime),
			CreatedAt: startTime,
			UpdatedAt: time.Now(),
		}

		return analysisResult, nil
	}

	// 5. Perform cat identification using thumbnail and caby_vision
	log.Printf("Performing cat identification for %s", record.VideoID)

	// Get thumbnail from backend_server
	thumbnailData, err := s.getThumbnailFromBackendServer(ctx, record)
	if err != nil {
		log.Printf("Failed to get thumbnail for %s: %v", record.VideoID, err)
		// Fall back to unknown cat
		analysisResult := &models.RecordAnalysis{
			VideoID:       record.VideoID,
			AnimalID:      "unknown",
			CatConfidence: 0.0,
			BehaviorType:  "normal_poop",
			IsAbnormal:    false,
			AbnormalType:  "",
			AbnormalProb:  0.05,
			AiResults: fmt.Sprintf(`{"keyframes":%d,"original_duration":%.2f,"processed_duration":%.2f,"fps":%.2f,"start_time":"%s","end_time":"%s","error":"failed to get thumbnail"}`,
				len(extractionResult.Keyframes), extractionResult.OriginalDuration, extractionResult.ProcessedDuration, extractionResult.FPS,
				record.StartTime, record.EndTime),
			CreatedAt: startTime,
			UpdatedAt: time.Now(),
		}
		return analysisResult, nil
	}

	// Use shadow mode for cat detection
	shadowResult, err := s.detectCatWithShadowMode(ctx, thumbnailData, userID)
	if err != nil {
		log.Printf("Failed to detect cat with shadow mode for %s: %v", record.VideoID, err)
		// Fall back to unknown cat
		analysisResult := &models.RecordAnalysis{
			VideoID:       record.VideoID,
			AnimalID:      "unknown",
			CatConfidence: 0.0,
			BehaviorType:  "normal_poop",
			IsAbnormal:    false,
			AbnormalType:  "",
			AbnormalProb:  0.05,
			AiResults: fmt.Sprintf(`{"keyframes":%d,"original_duration":%.2f,"processed_duration":%.2f,"fps":%.2f,"start_time":"%s","end_time":"%s","error":"failed to detect cat with shadow mode"}`,
				len(extractionResult.Keyframes), extractionResult.OriginalDuration, extractionResult.ProcessedDuration, extractionResult.FPS,
				record.StartTime, record.EndTime),
			CreatedAt: startTime,
			UpdatedAt: time.Now(),
		}
		return analysisResult, nil
	}

	// Map cat name to cat ID (use shadow result for final decision)
	finalCatName := shadowResult.ShadowResult
	identifiedCatID := "unknown"
	if catID, exists := catNameToIDMapping[finalCatName]; exists {
		identifiedCatID = catID
		log.Printf("Mapped shadow result cat name '%s' to ID '%s'", finalCatName, catID)
	} else {
		log.Printf("Unknown shadow result cat name '%s', using 'unknown' as ID", finalCatName)
	}

	// Create analysis result with shadow mode detection
	shadowResultJSON, _ := json.Marshal(shadowResult)
	analysisResult := &models.RecordAnalysis{
		VideoID:       record.VideoID,
		AnimalID:      identifiedCatID,
		CatConfidence: shadowResult.Confidence,
		BehaviorType:  "normal_poop",
		IsAbnormal:    false,
		AbnormalType:  "",
		AbnormalProb:  0.05,
		AiResults: fmt.Sprintf(`{"keyframes":%d,"original_duration":%.2f,"processed_duration":%.2f,"fps":%.2f,"start_time":"%s","end_time":"%s","shadow_mode_result":%s}`,
			len(extractionResult.Keyframes), extractionResult.OriginalDuration, extractionResult.ProcessedDuration, extractionResult.FPS,
			record.StartTime, record.EndTime, string(shadowResultJSON)),
		CreatedAt: startTime,
		UpdatedAt: time.Now(),
	}

	// 通过API发送影子模式结果给backend_server
	go func() {
		err := s.sendShadowResultToBackend(userID, record.VideoID, shadowResult)
		if err != nil {
			log.Printf("Failed to send shadow result to backend: %v", err)
		}
	}()

	// 6. Save analysis to Qdrant
	if err := s.qdrantClient.SaveAnalysis(ctx, analysisResult); err != nil {
		log.Printf("Error saving analysis to Qdrant: %v", err)
	} else {
		log.Printf("Successfully saved analysis to Qdrant for %s", record.VideoID)
	}

	log.Printf("Analysis completed for %s in %v", record.VideoID, time.Since(startTime))
	return analysisResult, nil
}

// sendShadowResultToBackend 发送影子模式结果到backend_server
func (s *Service) sendShadowResultToBackend(userID, videoID string, shadowResult *shadow.ShadowModeResult) error {
	// 构造请求数据
	requestData := map[string]interface{}{
		"user_id":       userID,
		"video_id":      videoID,
		"shadow_result": shadowResult,
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return fmt.Errorf("failed to marshal request data: %w", err)
	}

	// 发送POST请求到backend_server
	backendURL := fmt.Sprintf("%s/api/shadow/result", s.cfg.BackendServerURL)
	req, err := http.NewRequest("POST", backendURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", s.cfg.BackendServiceToken))

	// 发送请求
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("backend returned error status %d: %s", resp.StatusCode, string(body))
	}

	log.Printf("Successfully sent shadow result to backend for video %s", videoID)
	return nil
}

// formatDuration formats duration in seconds to H:MM:SS format
func formatDuration(seconds float64) string {
	dur := time.Duration(seconds * float64(time.Second))
	h := int(dur.Hours())
	m := int(dur.Minutes()) % 60
	s := int(dur.Seconds()) % 60
	return fmt.Sprintf("%d:%02d:%02d", h, m, s)
}

// withRetry 通用的重试函数，用于HTTP请求等操作
// opName: 操作名称，用于日志记录
// fn: 要执行的函数，返回错误时会重试
// shouldRetry: 判断是否应该重试的函数，接收错误并返回布尔值
func (s *Service) withRetry(ctx context.Context, opName string, fn func() error, shouldRetry func(error) bool) error {
	var lastErr error

	for attempt := 1; attempt <= maxDownloadRetries; attempt++ {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return fmt.Errorf("context cancelled before %s attempt %d: %w", opName, attempt, ctx.Err())
		default:
		}

		// 执行操作
		err := fn()
		if err == nil {
			return nil // 成功，返回
		}

		lastErr = fmt.Errorf("%s failed (attempt %d): %w", opName, attempt, err)
		log.Printf("Warning: %v", lastErr)

		// 判断是否应该重试
		if !shouldRetry(err) {
			log.Printf("%s failed with non-retryable error: %v", opName, err)
			return lastErr // 不应该重试的错误直接返回
		}

		// 达到最大重试次数
		if attempt >= maxDownloadRetries {
			break
		}

		// 等待后重试
		log.Printf("Retrying %s in %v...", opName, retryDelay)
		select {
		case <-time.After(retryDelay):
			// 继续重试
		case <-ctx.Done():
			return fmt.Errorf("context cancelled during retry delay: %w", ctx.Err())
		}
	}

	return fmt.Errorf("failed %s after %d attempts: %w", opName, maxDownloadRetries, lastErr)
}

// isNetworkError 判断是否为可重试的网络错误
func (s *Service) isNetworkError(err error) bool {
	// 这里可以根据具体错误类型进行判断
	// 例如，超时、连接重置等是可以重试的
	// 而权限错误、参数错误等则不应该重试
	if err == nil {
		return false
	}

	// 简单实现：认为所有错误都可重试
	// 在实际应用中，可以根据错误类型进行更细致的判断
	return true
}

// is404Error 检测是否为404错误
func (s *Service) is404Error(err error) bool {
	if err == nil {
		return false
	}

	// 检查错误字符串中是否包含"404"或"not found"
	errStr := err.Error()
	return strings.Contains(errStr, "404") || strings.Contains(errStr, "not found")
}

// extractKeyframesFromHLS extracts keyframes from an HLS stream
func (s *Service) extractKeyframesFromHLS(ctx context.Context, playlistURL string, outputDir string, threshold float64) (*KeyframeExtractionResult, error) {
	// 1. Fetch and parse the playlist
	playlist, err := s.fetchAndParsePlaylist(ctx, playlistURL)
	if err != nil {
		return nil, fmt.Errorf("failed to get HLS playlist: %w", err)
	}

	mediaPlaylist, ok := playlist.(*m3u8.MediaPlaylist)
	if !ok {
		return nil, fmt.Errorf("playlist is not a Media Playlist")
	}

	if len(mediaPlaylist.Segments) == 0 {
		return nil, fmt.Errorf("HLS playlist contains no segments")
	}

	log.Printf("Playlist URL: %s, Number of segments: %d", playlistURL, len(mediaPlaylist.Segments))

	// Calculate Original Duration from M3U8 #EXTINF tags
	var originalDuration float64
	for _, segment := range mediaPlaylist.Segments {
		if segment != nil {
			originalDuration += segment.Duration
		}
	}
	log.Printf("Calculated Original Duration from M3U8: %.2fs", originalDuration)

	// --- Correct Base URL Calculation ---
	parsedPlaylistURL, err := url.Parse(playlistURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse playlist URL: %w", err)
	}
	// Base URL should be scheme://host (e.g., http://***************:5678)
	baseURL := fmt.Sprintf("%s://%s", parsedPlaylistURL.Scheme, parsedPlaylistURL.Host)
	// --- End Correct Base URL Calculation ---

	log.Printf("Base URL for segments: %s", baseURL)

	// 设置处理的最大并发数，这样可以控制CPU使用率
	maxConcurrentSegments := 1 // 默认按顺序处理

	// 使用更合理的资源分配
	if s.cfg.Server.MaxConcurrency > 0 {
		maxConcurrentSegments = s.cfg.Server.MaxConcurrency
	}

	log.Printf("Using max concurrent segments: %d", maxConcurrentSegments)

	// Create mats for processing - 使用预分配的矩阵池来减少内存分配和GC压力
	var keyframes []KeyframeInfo
	prevFrameGray := gocv.NewMat()
	defer prevFrameGray.Close()
	frame := gocv.NewMat()
	defer frame.Close()
	frameGray := gocv.NewMat()
	defer frameGray.Close()
	diff := gocv.NewMat()
	defer diff.Close()

	totalFrames := 0
	processedDuration := 0.0
	estimatedFPS := 30.0 // Default FPS

	// 设置进度追踪
	totalSegments := 0
	for _, seg := range mediaPlaylist.Segments {
		if seg != nil {
			totalSegments++
		}
	}

	// 为了节省内存，每处理N个分段就进行一次垃圾回收
	gcInterval := 5

	// Process segments sequentially
	for segIndex, segment := range mediaPlaylist.Segments {
		if segment == nil {
			continue
		}

		// 强制垃圾回收以释放内存
		if segIndex > 0 && segIndex%gcInterval == 0 {
			runtime.GC()
		}

		// 周期性报告进度
		if segIndex%5 == 0 || segIndex == len(mediaPlaylist.Segments)-1 {
			progress := float64(segIndex+1) / float64(totalSegments) * 100
			log.Printf("Processing segment %d/%d (%.1f%%)...",
				segIndex+1, totalSegments, progress)

			// 每个进度点检查内存使用情况
			var m runtime.MemStats
			runtime.ReadMemStats(&m)
			log.Printf("Memory usage: Alloc=%vMB, Sys=%vMB",
				m.Alloc/1024/1024, m.Sys/1024/1024)
		}

		// Check for context cancellation
		select {
		case <-ctx.Done():
			return nil, fmt.Errorf("context cancelled: %w", ctx.Err())
		default:
			// Continue processing
		}

		segmentURL := segment.URI
		if !strings.HasPrefix(segmentURL, "http") {
			// Resolve relative URL based on the *path* of the playlist URL
			// The segment URI is relative to the playlist's path, not just the host.
			resolvedRef, err := parsedPlaylistURL.Parse(segmentURL)
			if err != nil {
				log.Printf("Warning: Failed to resolve relative segment URL %s against base %s: %v", segmentURL, playlistURL, err)
				continue
			}
			segmentURL = resolvedRef.String()
			log.Printf("Resolved relative segment URL: %s", segmentURL)
		} else {
			log.Printf("Absolute segment URL: %s", segmentURL)
		}

		// Download segment to temporary file
		segmentFileName := fmt.Sprintf("segment_%d.ts", segIndex)
		tempSegmentPath := filepath.Join(outputDir, segmentFileName)

		if err := s.downloadSegment(ctx, segmentURL, tempSegmentPath); err != nil {
			log.Printf("Warning: Failed to download segment %s: %v", segmentURL, err)
			continue // Skip this segment
		}

		// Process the segment
		vc, err := gocv.VideoCaptureFile(tempSegmentPath)
		if err != nil {
			log.Printf("Warning: Error opening segment %s: %v", tempSegmentPath, err)
			os.Remove(tempSegmentPath)
			continue // Skip this segment, but don't return error for the whole process yet
		}

		if !vc.IsOpened() {
			log.Printf("Warning: Video capture not opened for segment %s", tempSegmentPath)
			vc.Close()
			os.Remove(tempSegmentPath)
			continue // Skip this segment
		}

		fps := vc.Get(gocv.VideoCaptureFPS)
		if fps <= 0 {
			fps = estimatedFPS
		} else {
			estimatedFPS = fps
		}

		localFrameCount := 0
		lastKeyframeTime := time.Now()

		// Process frames in this segment
		for {
			if ok := vc.Read(&frame); !ok || frame.Empty() {
				break
			}

			localFrameCount++
			totalFrames++

			// 定期让出CPU时间，避免100%占用
			if localFrameCount%15 == 0 {
				// 短暂休眠，让出CPU时间给其他进程
				time.Sleep(time.Millisecond * 1)
			}

			// Timestamp based on processed duration and frames
			timestamp := processedDuration + (float64(localFrameCount) / fps)
			timeStr := formatDuration(timestamp)

			// 优化转换过程 - 避免不必要的内存分配
			gocv.CvtColor(frame, &frameGray, gocv.ColorBGRToGray)

			isKeyframe := false
			if prevFrameGray.Empty() {
				isKeyframe = true // First frame is a keyframe
			} else {
				// 高效的帧差异检测
				gocv.AbsDiff(frameGray, prevFrameGray, &diff)
				meanVal := diff.Mean()
				meanDiff := meanVal.Val1
				if meanDiff > threshold {
					isKeyframe = true
				}
			}

			if isKeyframe {
				// 限制关键帧提取速率，避免短时间内生成过多关键帧
				now := time.Now()
				if len(keyframes) > 0 && now.Sub(lastKeyframeTime) < 100*time.Millisecond {
					// 如果与上一帧时间间隔过短，跳过这一帧
					continue
				}
				lastKeyframeTime = now

				filename := fmt.Sprintf("keyframe_%d.jpg", len(keyframes)+1)
				outputPath := filepath.Join(outputDir, filename)

				// 使用更高效的方式保存图像
				// 保存关键帧图像
				// GoCV的IMWrite不支持直接设置压缩参数，使用默认设置
				if ok := gocv.IMWrite(outputPath, frame); !ok {
					log.Printf("Warning: Failed to write keyframe image %s", outputPath)
				}

				keyframes = append(keyframes, KeyframeInfo{
					FrameNumber: totalFrames,
					Timestamp:   timestamp, // Use timestamp based on processing
					TimeStr:     timeStr,
					Filename:    filename,
				})

				// 更高效的内存管理 - 创建新矩阵而非关闭并克隆
				newPrevFrameGray := gocv.NewMat()
				frameGray.CopyTo(&newPrevFrameGray)

				// 释放旧矩阵并更新引用
				if !prevFrameGray.Empty() {
					prevFrameGray.Close()
				}
				prevFrameGray = newPrevFrameGray
			}
		}

		vc.Close()

		// Update processed duration and clean up segment file
		segmentProcessedDuration := float64(localFrameCount) / fps
		processedDuration += segmentProcessedDuration // Accumulate processed duration
		os.Remove(tempSegmentPath)
	}

	// Check if any keyframes were extracted at all after processing all segments
	if len(keyframes) == 0 && totalFrames == 0 {
		log.Printf("No frames or keyframes could be processed from HLS stream %s", playlistURL)
		// Decide if this is an error or just an empty result
		// Returning an error might be appropriate if segments existed but couldn't be processed
		// If the playlist was valid but segments were empty/unreadable, totalFrames might still be 0
		// Let's return success but with empty results for now.
	}

	// Calculate time between keyframes (based on processed timestamps)
	for i := 0; i < len(keyframes)-1; i++ {
		keyframes[i].TimeToNext = keyframes[i+1].Timestamp - keyframes[i].Timestamp
		keyframes[i].TimeToNextStr = formatDuration(keyframes[i].TimeToNext)
	}

	if len(keyframes) > 0 {
		keyframes[len(keyframes)-1].TimeToNext = -1
		keyframes[len(keyframes)-1].TimeToNextStr = "END"
	}

	log.Printf("Keyframe extraction complete: %d frames processed, %d keyframes extracted",
		totalFrames, len(keyframes))

	// Save metadata JSON
	jsonData := map[string]interface{}{
		"source_playlist":    playlistURL,
		"total_frames":       totalFrames,
		"estimated_fps":      estimatedFPS,
		"original_duration":  originalDuration,  // Add original duration
		"processed_duration": processedDuration, // Add processed duration
		"keyframes":          keyframes,
	}

	jsonBytes, _ := json.MarshalIndent(jsonData, "", "  ")
	jsonFile := filepath.Join(outputDir, "keyframes_meta.json")
	if err := os.WriteFile(jsonFile, jsonBytes, 0644); err != nil {
		log.Printf("Warning: Failed to write keyframe metadata: %v", err)
	}

	return &KeyframeExtractionResult{ // Return non-nil result on success
		Keyframes:         keyframes,
		TotalFrames:       totalFrames,
		ProcessedDuration: processedDuration, // Use processed duration here
		OriginalDuration:  originalDuration,  // Include original duration
		FPS:               estimatedFPS,
		TempDir:           outputDir,
	}, nil
}

// fetchAndParsePlaylist fetches and parses an M3U8 playlist
func (s *Service) fetchAndParsePlaylist(ctx context.Context, playlistURL string) (m3u8.Playlist, error) {
	var playlist m3u8.Playlist
	var listType m3u8.ListType

	err := s.withRetry(ctx, "fetch_playlist", func() error {
		// 创建请求
		req, err := http.NewRequestWithContext(ctx, "GET", playlistURL, nil)
		if err != nil {
			return fmt.Errorf("failed to create request: %w", err)
		}

		// 发送请求
		resp, err := s.httpClient.Do(req)
		if err != nil {
			return fmt.Errorf("failed to fetch playlist: %w", err)
		}
		defer resp.Body.Close()

		// 检查状态码
		if resp.StatusCode != http.StatusOK {
			bodyBytes, _ := io.ReadAll(io.LimitReader(resp.Body, 1024))
			if resp.StatusCode == http.StatusNotFound {
				return fmt.Errorf("playlist not found (404): %s", string(bodyBytes))
			}
			return fmt.Errorf("unexpected status code %d: %s", resp.StatusCode, string(bodyBytes))
		}

		// 解析播放列表
		var decodeErr error
		playlist, listType, decodeErr = m3u8.DecodeFrom(resp.Body, true)
		if decodeErr != nil {
			return fmt.Errorf("failed to decode playlist: %w", decodeErr)
		}

		return nil
	}, func(err error) bool {
		// 404错误不重试，其他错误重试
		return !s.is404Error(err) && s.isNetworkError(err)
	})

	if err != nil {
		return nil, err
	}

	// 检查播放列表类型
	if listType != m3u8.MEDIA {
		return nil, fmt.Errorf("playlist is not a Media Playlist (type: %v)", listType)
	}

	return playlist, nil
}

// getThumbnailFromBackendServer gets thumbnail image data from backend_server
func (s *Service) getThumbnailFromBackendServer(ctx context.Context, record *models.RecordShit) ([]byte, error) {
	// Parse start time to get the timestamp for thumbnail
	startTime, err := time.Parse(time.RFC3339, record.StartTime)
	if err != nil {
		return nil, fmt.Errorf("failed to parse start time: %w", err)
	}

	// Construct thumbnail URL
	// Format: /api/records/videos/thumbnail/{folder}?bucket={bucket}
	// folder is the timestamp, bucket contains device info
	folderName := url.QueryEscape(startTime.Format(time.RFC3339))
	bucket := fmt.Sprintf("records/device%s", record.DeviceID) // Use new bucket format: records/device{deviceID}

	thumbnailURL := fmt.Sprintf("%s/api/records/videos/thumbnail/%s?bucket=%s",
		s.cfg.BackendServerURL, folderName, bucket)

	log.Printf("Requesting thumbnail from: %s", thumbnailURL)

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "GET", thumbnailURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create thumbnail request: %w", err)
	}

	// Add authentication if needed (assuming same service token)
	if s.cfg.Auth.ServiceToken != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", s.cfg.Auth.ServiceToken))
	}

	// Send request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get thumbnail: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(io.LimitReader(resp.Body, 1024))
		return nil, fmt.Errorf("thumbnail request failed with status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Read thumbnail data
	thumbnailData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read thumbnail data: %w", err)
	}

	log.Printf("Successfully retrieved thumbnail, size: %d bytes", len(thumbnailData))
	return thumbnailData, nil
}

// detectCatFromThumbnail sends thumbnail to caby_vision for cat detection
func (s *Service) detectCatFromThumbnail(ctx context.Context, thumbnailData []byte) (string, float64, map[string]float64, error) {
	// Convert thumbnail to base64
	imageBase64 := base64.StdEncoding.EncodeToString(thumbnailData)

	// Prepare request payload for caby_vision
	payload := map[string]interface{}{
		"image":             imageBase64,
		"return_features":   false,
		"return_confidence": true,
		"task":              "predict",
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return "", 0, nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Construct caby_vision URL
	visionURL := fmt.Sprintf("http://%s:%d/predict", s.cfg.Vision.Host, s.cfg.Vision.Port)
	log.Printf("Sending cat detection request to: %s", visionURL)

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", visionURL, strings.NewReader(string(jsonData)))
	if err != nil {
		return "", 0, nil, fmt.Errorf("failed to create vision request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if s.cfg.Vision.ApiKey != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", s.cfg.Vision.ApiKey))
	}

	// Send request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return "", 0, nil, fmt.Errorf("failed to send vision request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", 0, nil, fmt.Errorf("failed to read vision response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", 0, nil, fmt.Errorf("vision request failed with status %d: %s", resp.StatusCode, string(responseBody))
	}

	// Parse response
	var visionResponse map[string]interface{}
	if err := json.Unmarshal(responseBody, &visionResponse); err != nil {
		return "", 0, nil, fmt.Errorf("failed to parse vision response: %w", err)
	}

	// Check success status
	success, successExists := visionResponse["success"].(bool)
	if successExists && !success {
		errorMsg, _ := visionResponse["error"].(string)
		return "", 0, nil, fmt.Errorf("vision API error: %s", errorMsg)
	}

	// Extract results
	var results map[string]interface{}
	if successExists {
		resultsData, ok := visionResponse["results"].(map[string]interface{})
		if !ok {
			return "", 0, nil, fmt.Errorf("invalid response format from vision")
		}
		results = resultsData
	} else {
		results = visionResponse
	}

	// Extract cat detection results
	predictedCat, _ := results["predicted_cat"].(string)
	confidence, _ := results["confidence"].(float64)

	// Extract class probabilities
	classProbabilities := make(map[string]float64)
	if classProbsRaw, ok := results["class_probabilities"].(map[string]interface{}); ok {
		for k, v := range classProbsRaw {
			if prob, ok := v.(float64); ok {
				classProbabilities[k] = prob
			}
		}
	}

	log.Printf("Cat detection result: %s (confidence: %.4f)", predictedCat, confidence)
	return predictedCat, confidence, classProbabilities, nil
}

// detectCatWithShadowMode 使用影子模式进行猫咪检测
func (s *Service) detectCatWithShadowMode(ctx context.Context, thumbnailData []byte, userID string) (*shadow.ShadowModeResult, error) {
	if !s.cfg.ShadowMode.Enabled {
		// 影子模式未启用，使用原始方法
		catName, confidence, _, err := s.detectCatFromThumbnail(ctx, thumbnailData)
		if err != nil {
			return nil, err
		}

		return &shadow.ShadowModeResult{
			OriginalResult: catName,
			ShadowResult:   catName,
			Similarity:     confidence,
			IsNewCat:       false,
			Confidence:     confidence,
			Features:       []float64{},
			FeatureDim:     0,
			ModelVersion:   "original",
			Timestamp:      time.Now().Format(time.RFC3339),
		}, nil
	}

	// 转换为base64
	imageBase64 := base64.StdEncoding.EncodeToString(thumbnailData)

	// 并行调用原始API和影子模式API
	type originalResult struct {
		catName            string
		confidence         float64
		classProbabilities map[string]float64
		err                error
	}

	type shadowResult struct {
		response *vision.FeaturedResponse
		err      error
	}

	originalChan := make(chan originalResult, 1)
	shadowChan := make(chan shadowResult, 1)

	// 调用原始API
	go func() {
		catName, confidence, classProbabilities, err := s.detectCatFromThumbnail(ctx, thumbnailData)
		originalChan <- originalResult{catName, confidence, classProbabilities, err}
	}()

	// 调用影子模式API
	go func() {
		response, err := s.featuredClient.ExtractFeatures(imageBase64, userID)
		shadowChan <- shadowResult{response, err}
	}()

	// 等待两个结果
	var origResult originalResult
	var shadResult shadowResult

	for i := 0; i < 2; i++ {
		select {
		case origResult = <-originalChan:
		case shadResult = <-shadowChan:
		case <-ctx.Done():
			return nil, ctx.Err()
		}
	}

	// 检查错误
	if origResult.err != nil {
		log.Printf("Original API error: %v", origResult.err)
		// 如果原始API失败，仍然可以继续使用影子模式结果
	}

	if shadResult.err != nil {
		log.Printf("Shadow mode API error: %v", shadResult.err)
		// 如果影子模式失败，返回原始结果
		return &shadow.ShadowModeResult{
			OriginalResult: origResult.catName,
			ShadowResult:   origResult.catName,
			Similarity:     origResult.confidence,
			IsNewCat:       false,
			Confidence:     origResult.confidence,
			Features:       []float64{},
			FeatureDim:     0,
			ModelVersion:   "original_fallback",
			Timestamp:      time.Now().Format(time.RFC3339),
		}, nil
	}

	// 处理影子模式结果
	shadowModeResult, err := s.shadowService.ProcessShadowMode(ctx, userID, shadResult.response.Features, origResult.catName)
	if err != nil {
		log.Printf("Shadow mode processing error: %v", err)
		// 处理失败，返回原始结果
		return &shadow.ShadowModeResult{
			OriginalResult: origResult.catName,
			ShadowResult:   origResult.catName,
			Similarity:     origResult.confidence,
			IsNewCat:       false,
			Confidence:     origResult.confidence,
			Features:       shadResult.response.Features,
			FeatureDim:     shadResult.response.FeatureDim,
			ModelVersion:   shadResult.response.ModelVersion,
			Timestamp:      time.Now().Format(time.RFC3339),
		}, nil
	}

	return shadowModeResult, nil
}

// downloadSegment downloads a single HLS segment to a file with retry logic
func (s *Service) downloadSegment(ctx context.Context, segmentURL string, destPath string) error {
	return s.withRetry(ctx, "download_segment", func() error {
		// 创建请求
		req, err := http.NewRequestWithContext(ctx, "GET", segmentURL, nil)
		if err != nil {
			return fmt.Errorf("failed to create request: %w", err)
		}

		// 发送请求
		resp, err := s.httpClient.Do(req)
		if err != nil {
			return fmt.Errorf("failed to download segment: %w", err)
		}
		defer resp.Body.Close()

		// 检查状态码
		if resp.StatusCode != http.StatusOK {
			bodyBytes, _ := io.ReadAll(io.LimitReader(resp.Body, 1024))
			if resp.StatusCode == http.StatusNotFound {
				return fmt.Errorf("segment not found (404): %s", string(bodyBytes))
			}
			return fmt.Errorf("unexpected status code %d: %s", resp.StatusCode, string(bodyBytes))
		}

		// 创建输出文件
		outFile, err := os.Create(destPath)
		if err != nil {
			return fmt.Errorf("failed to create segment file: %w", err)
		}
		defer outFile.Close()

		// 复制数据
		_, err = io.Copy(outFile, resp.Body)
		if err != nil {
			os.Remove(destPath) // 清理部分文件
			return fmt.Errorf("failed to write segment data: %w", err)
		}

		return nil
	}, func(err error) bool {
		// 404错误不重试，其他错误重试
		return !s.is404Error(err) && s.isNetworkError(err)
	})
}
