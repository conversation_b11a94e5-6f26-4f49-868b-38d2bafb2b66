package catmanager

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"caby-ai/config"
	"caby-ai/pkg/shadow"
)

// Service 猫咪管理服务
type Service struct {
	config     *config.Config
	httpClient *http.Client
}

// NewCatRequest 创建新猫咪请求
type NewCatRequest struct {
	Name      string    `json:"name"`
	UserID    string    `json:"user_id"`
	Gender    int8      `json:"gender"`
	Birthday  time.Time `json:"birthday"`
	Breed     string    `json:"breed"`
	Color     string    `json:"color"`
	Weight    float64   `json:"weight"`
	AvatarURL string    `json:"avatar_url"`
}

// NewCatResponse 创建新猫咪响应
type NewCatResponse struct {
	Success bool   `json:"success"`
	CatID   string `json:"cat_id"`
	Message string `json:"message"`
	Error   string `json:"error,omitempty"`
}

// CatFeatureRequest 存储猫咪特征请求
type CatFeatureRequest struct {
	CatID          string    `json:"cat_id"`
	UserID         string    `json:"user_id"`
	Features       []float64 `json:"features"`
	ModelVersion   string    `json:"model_version"`
	FeatureVersion string    `json:"feature_version"`
}

// NewService 创建新的猫咪管理服务
func NewService(config *config.Config) *Service {
	return &Service{
		config: config,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// GenerateNewCatID 生成新猫咪ID
func (s *Service) GenerateNewCatID() string {
	timestamp := time.Now().Format("20060102150405")
	return fmt.Sprintf("NewCat%s", timestamp)
}

// GenerateNewCatName 生成新猫咪名称
func (s *Service) GenerateNewCatName() string {
	timestamp := time.Now().Format("20060102150405")
	return fmt.Sprintf("NewCat%s", timestamp)
}

// CreateNewCat 在backend_server中创建新猫咪记录
func (s *Service) CreateNewCat(ctx context.Context, userID string, shadowResult *shadow.ShadowModeResult) (*NewCatResponse, error) {
	// 生成新猫咪ID和名称
	catID := s.GenerateNewCatID()
	catName := shadowResult.ShadowResult
	
	// 如果影子模式没有提供名称，使用生成的名称
	if catName == "" || catName == "unknown" {
		catName = s.GenerateNewCatName()
	}
	
	// 准备创建猫咪的请求
	newCatReq := NewCatRequest{
		Name:     catName,
		UserID:   userID,
		Gender:   0, // 未知性别
		Birthday: time.Now(),
		Breed:    "Unknown",
		Color:    "Unknown",
		Weight:   0.0,
	}
	
	// 序列化请求
	jsonData, err := json.Marshal(newCatReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal new cat request: %w", err)
	}
	
	// 构建请求URL
	url := fmt.Sprintf("%s/api/cats", s.config.BackendServerURL)
	
	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	
	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", s.config.BackendServiceToken))
	
	// 发送请求
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()
	
	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}
	
	// 检查HTTP状态码
	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("backend server error: status %d, body: %s", resp.StatusCode, string(responseBody))
	}
	
	// 解析响应
	var response NewCatResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}
	
	// 如果backend返回了cat_id，使用它；否则使用我们生成的
	if response.CatID == "" {
		response.CatID = catID
	}
	
	log.Printf("Created new cat: ID=%s, Name=%s, UserID=%s", response.CatID, catName, userID)
	return &response, nil
}

// StoreCatFeatures 存储猫咪特征到Qdrant
func (s *Service) StoreCatFeatures(ctx context.Context, shadowService *shadow.Service, catID, userID string, shadowResult *shadow.ShadowModeResult) error {
	if len(shadowResult.Features) == 0 {
		return fmt.Errorf("no features to store")
	}
	
	// 创建猫咪特征数据
	catFeature := shadow.CatFeature{
		CatID:          catID,
		CatName:        shadowResult.ShadowResult,
		UserID:         userID,
		Features:       shadowResult.Features,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
		FeatureVersion: shadowResult.ModelVersion,
	}
	
	// 存储到Qdrant
	err := shadowService.StoreCatFeature(ctx, catFeature)
	if err != nil {
		return fmt.Errorf("failed to store cat features: %w", err)
	}
	
	log.Printf("Stored features for cat: ID=%s, UserID=%s, FeatureDim=%d", catID, userID, len(shadowResult.Features))
	return nil
}

// ProcessNewCatDetection 处理新猫咪检测的完整流程
func (s *Service) ProcessNewCatDetection(ctx context.Context, shadowService *shadow.Service, userID string, shadowResult *shadow.ShadowModeResult) (string, error) {
	if !shadowResult.IsNewCat {
		return shadowResult.MatchedCatID, nil
	}
	
	// 创建新猫咪记录
	newCatResp, err := s.CreateNewCat(ctx, userID, shadowResult)
	if err != nil {
		return "", fmt.Errorf("failed to create new cat: %w", err)
	}
	
	if !newCatResp.Success {
		return "", fmt.Errorf("backend server failed to create cat: %s", newCatResp.Error)
	}
	
	// 存储猫咪特征
	err = s.StoreCatFeatures(ctx, shadowService, newCatResp.CatID, userID, shadowResult)
	if err != nil {
		log.Printf("Warning: failed to store features for new cat %s: %v", newCatResp.CatID, err)
		// 不返回错误，因为猫咪记录已经创建成功
	}
	
	return newCatResp.CatID, nil
}

// UpdateExistingCatFeatures 更新现有猫咪的特征
func (s *Service) UpdateExistingCatFeatures(ctx context.Context, shadowService *shadow.Service, catID, userID string, shadowResult *shadow.ShadowModeResult) error {
	if len(shadowResult.Features) == 0 {
		return nil // 没有特征需要更新
	}
	
	// 创建更新的猫咪特征数据
	catFeature := shadow.CatFeature{
		CatID:          catID,
		CatName:        shadowResult.ShadowResult,
		UserID:         userID,
		Features:       shadowResult.Features,
		CreatedAt:      time.Now(), // 这会被UpdateCatFeature忽略
		UpdatedAt:      time.Now(),
		FeatureVersion: shadowResult.ModelVersion,
	}
	
	// 更新Qdrant中的特征
	err := shadowService.UpdateCatFeature(ctx, catFeature)
	if err != nil {
		return fmt.Errorf("failed to update cat features: %w", err)
	}
	
	log.Printf("Updated features for existing cat: ID=%s, UserID=%s", catID, userID)
	return nil
}

// SyncCatIDWithBackend 与backend同步猫咪ID
func (s *Service) SyncCatIDWithBackend(ctx context.Context, tempCatID, finalCatID, userID string) error {
	// 这里可以实现与backend的同步逻辑
	// 例如更新临时ID到最终ID的映射
	log.Printf("Syncing cat ID: temp=%s, final=%s, user=%s", tempCatID, finalCatID, userID)
	return nil
}

// GetCatIDMapping 获取猫咪名称到ID的映射
func (s *Service) GetCatIDMapping() map[string]string {
	// 临时映射，实际应该从数据库获取
	return map[string]string{
		"小黑": "f3ce1b02b2c1d755421000",
		"小白": "f3ce1b02b40e9477c21000",
		"小花": "f3ce1b02b40ed223821000",
	}
}

// MapCatNameToID 将猫咪名称映射到ID
func (s *Service) MapCatNameToID(catName string) string {
	mapping := s.GetCatIDMapping()
	if catID, exists := mapping[catName]; exists {
		return catID
	}
	return "unknown"
}
