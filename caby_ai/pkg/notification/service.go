package notification

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"caby-ai/config"
	"caby-ai/pkg/shadow"
)

// Service 通知服务
type Service struct {
	config     *config.Config
	httpClient *http.Client
}

// NotificationRequest 通知请求
type NotificationRequest struct {
	UserID      string `json:"user_id"`
	Title       string `json:"title"`
	Content     string `json:"content"`
	Type        string `json:"type"`
	VideoID     string `json:"video_id,omitempty"`
	ExtraData   string `json:"extra_data,omitempty"`
}

// NotificationResponse 通知响应
type NotificationResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Error   string `json:"error,omitempty"`
}

// ShadowModeNotification 影子模式通知数据
type ShadowModeNotification struct {
	UserID           string  `json:"user_id"`
	VideoID          string  `json:"video_id"`
	NotificationType string  `json:"notification_type"`
	OriginalResult   string  `json:"original_result"`
	ShadowResult     string  `json:"shadow_result"`
	SimilarityScore  float64 `json:"similarity_score"`
	MessageTitle     string  `json:"message_title"`
	MessageContent   string  `json:"message_content"`
}

// NewService 创建新的通知服务
func NewService(config *config.Config) *Service {
	return &Service{
		config: config,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// SendNotification 发送通知到backend_server
func (s *Service) SendNotification(ctx context.Context, notification *NotificationRequest) error {
	// 序列化请求
	jsonData, err := json.Marshal(notification)
	if err != nil {
		return fmt.Errorf("failed to marshal notification request: %w", err)
	}

	// 构建请求URL
	url := fmt.Sprintf("%s/api/notifications", s.config.BackendServerURL)

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", s.config.BackendServiceToken))

	// 发送请求
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send notification: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("notification failed: status %d, body: %s", resp.StatusCode, string(responseBody))
	}

	log.Printf("Notification sent successfully to user %s", notification.UserID)
	return nil
}

// ProcessShadowModeNotification 处理影子模式通知
func (s *Service) ProcessShadowModeNotification(ctx context.Context, userID, videoID string, shadowResult *shadow.ShadowModeResult) error {
	// 检查是否需要发送通知
	shouldNotify, notificationType := s.shouldSendNotification(shadowResult)
	if !shouldNotify {
		return nil
	}

	// 生成通知内容
	title, content := s.generateNotificationContent(shadowResult, notificationType)

	// 创建通知请求
	notification := &NotificationRequest{
		UserID:    userID,
		Title:     title,
		Content:   content,
		Type:      "shadow_mode",
		VideoID:   videoID,
		ExtraData: s.createExtraData(shadowResult),
	}

	// 发送通知
	err := s.SendNotification(ctx, notification)
	if err != nil {
		log.Printf("Failed to send shadow mode notification: %v", err)
		return err
	}

	// 记录通知到数据库
	err = s.recordNotificationToDatabase(ctx, userID, videoID, shadowResult, notificationType, title, content)
	if err != nil {
		log.Printf("Failed to record notification to database: %v", err)
		// 不返回错误，因为通知已经发送成功
	}

	return nil
}

// shouldSendNotification 判断是否应该发送通知
func (s *Service) shouldSendNotification(shadowResult *shadow.ShadowModeResult) (bool, string) {
	// 检测到新猫咪
	if shadowResult.IsNewCat {
		return true, "new_cat"
	}

	// 影子模式结果与原始结果不一致
	if shadowResult.OriginalResult != shadowResult.ShadowResult {
		return true, "different_result"
	}

	// 置信度较低
	if shadowResult.Confidence < 0.7 {
		return true, "low_confidence"
	}

	return false, ""
}

// generateNotificationContent 生成通知内容
func (s *Service) generateNotificationContent(shadowResult *shadow.ShadowModeResult, notificationType string) (string, string) {
	switch notificationType {
	case "new_cat":
		title := fmt.Sprintf("发现新猫咪：%s", shadowResult.ShadowResult)
		content := fmt.Sprintf("系统检测到一只新的猫咪，已自动命名为"%s"。您可以在猫咪管理中修改名称和添加更多信息。", shadowResult.ShadowResult)
		return title, content

	case "different_result":
		// 格式：原始结果（影子模式结果）
		title := fmt.Sprintf("%s（%s）", shadowResult.OriginalResult, shadowResult.ShadowResult)
		content := fmt.Sprintf("影子模式识别结果与原始结果不一致。原始识别：%s，影子模式识别：%s，相似度：%.2f", 
			shadowResult.OriginalResult, shadowResult.ShadowResult, shadowResult.Similarity)
		return title, content

	case "low_confidence":
		title := fmt.Sprintf("识别置信度较低：%s", shadowResult.ShadowResult)
		content := fmt.Sprintf("猫咪识别的置信度较低（%.2f），建议检查图像质量或更新猫咪特征数据。", shadowResult.Confidence)
		return title, content

	default:
		title := fmt.Sprintf("影子模式通知：%s", shadowResult.ShadowResult)
		content := "影子模式检测到异常情况，请查看详细信息。"
		return title, content
	}
}

// createExtraData 创建额外数据
func (s *Service) createExtraData(shadowResult *shadow.ShadowModeResult) string {
	extraData := map[string]interface{}{
		"original_result":   shadowResult.OriginalResult,
		"shadow_result":     shadowResult.ShadowResult,
		"similarity":        shadowResult.Similarity,
		"is_new_cat":       shadowResult.IsNewCat,
		"matched_cat_id":   shadowResult.MatchedCatID,
		"confidence":       shadowResult.Confidence,
		"model_version":    shadowResult.ModelVersion,
		"timestamp":        shadowResult.Timestamp,
	}

	jsonData, _ := json.Marshal(extraData)
	return string(jsonData)
}

// recordNotificationToDatabase 记录通知到数据库
func (s *Service) recordNotificationToDatabase(ctx context.Context, userID, videoID string, shadowResult *shadow.ShadowModeResult, notificationType, title, content string) error {
	// 准备数据库记录请求
	dbRecord := ShadowModeNotification{
		UserID:           userID,
		VideoID:          videoID,
		NotificationType: notificationType,
		OriginalResult:   shadowResult.OriginalResult,
		ShadowResult:     shadowResult.ShadowResult,
		SimilarityScore:  shadowResult.Similarity,
		MessageTitle:     title,
		MessageContent:   content,
	}

	// 序列化请求
	jsonData, err := json.Marshal(dbRecord)
	if err != nil {
		return fmt.Errorf("failed to marshal db record: %w", err)
	}

	// 构建请求URL
	url := fmt.Sprintf("%s/api/shadow-notifications", s.config.BackendServerURL)

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", s.config.BackendServiceToken))

	// 发送请求
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to record notification: %w", err)
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		responseBody, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to record notification: status %d, body: %s", resp.StatusCode, string(responseBody))
	}

	log.Printf("Notification recorded to database for user %s, video %s", userID, videoID)
	return nil
}

// GetUserNotificationSettings 获取用户通知设置
func (s *Service) GetUserNotificationSettings(ctx context.Context, userID string) (bool, error) {
	// 构建请求URL
	url := fmt.Sprintf("%s/api/users/%s/shadow-config", s.config.BackendServerURL, userID)

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return false, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", s.config.BackendServiceToken))

	// 发送请求
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return false, fmt.Errorf("failed to get notification settings: %w", err)
	}
	defer resp.Body.Close()

	// 如果用户配置不存在，默认启用通知
	if resp.StatusCode == http.StatusNotFound {
		return true, nil
	}

	if resp.StatusCode != http.StatusOK {
		return false, fmt.Errorf("failed to get notification settings: status %d", resp.StatusCode)
	}

	// 解析响应
	var config struct {
		NotificationEnabled bool `json:"notification_enabled"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&config); err != nil {
		return false, fmt.Errorf("failed to decode response: %w", err)
	}

	return config.NotificationEnabled, nil
}
