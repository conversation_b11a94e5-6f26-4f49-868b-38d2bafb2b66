# 影子模式部署指南

## 概述

影子模式是一个基于特征向量的猫咪识别系统，用于与现有识别系统并行运行，提供更准确的识别结果和新猫咪发现功能。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   backend_server │    │     caby_ai     │    │   caby_vision   │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 原始识别API │ │◄───┤ │ 双API调用   │ │◄───┤ │ 原始模型    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │        │        │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 影子模式存储│ │◄───┤ │ 相似度比较  │ │◄───┤ │ 特征模型    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │        │        │    │                 │
│ ┌─────────────┐ │    │        ▼        │    └─────────────────┘
│ │ 通知系统    │ │◄───┤ ┌─────────────┐ │
│ └─────────────┘ │    │ │   Qdrant    │ │
└─────────────────┘    │ │ 向量数据库  │ │
                       │ └─────────────┘ │
                       └─────────────────┘
```

## 部署前准备

### 1. 环境要求

- Docker >= 20.10
- Docker Compose >= 1.29
- MySQL >= 8.0
- Python >= 3.8 (用于测试脚本)

### 2. 模型文件准备

确保以下模型文件存在：
```
../caby_training/reid/infrared_cat_recognition_project/deployment/
├── infrared_cat_model_quantized.onnx
└── reference_features.json
```

### 3. 数据库准备

确保MySQL数据库可访问，并有足够权限执行DDL操作。

## 快速部署

### 1. 使用自动部署脚本

```bash
# 进入项目目录
cd /path/to/caby_server

# 运行部署脚本
./scripts/deploy-shadow-mode.sh

# 按提示操作：
# - 确认模型文件路径
# - 输入数据库连接信息
# - 选择是否执行数据库迁移
```

### 2. 手动部署步骤

#### 步骤1: 复制模型文件

```bash
mkdir -p caby_vision/models/featured
cp ../caby_training/reid/infrared_cat_recognition_project/deployment/infrared_cat_model_quantized.onnx \
   caby_vision/models/featured/featured_cat_model_quantized.onnx
cp ../caby_training/reid/infrared_cat_recognition_project/deployment/reference_features.json \
   caby_vision/models/featured/reference_features.json
```

#### 步骤2: 配置环境变量

```bash
# 复制环境变量模板
cp caby_ai/.env.example caby_ai/.env
cp caby_vision/env.example caby_vision/.env

# 编辑配置文件
vim caby_ai/.env
vim caby_vision/.env
```

#### 步骤3: 执行数据库迁移

```bash
mysql -h localhost -u root -p cat_toilet_db < backend_server/database/migrations/add_shadow_mode_columns.sql
```

#### 步骤4: 启动服务

```bash
# 创建网络
docker network create caby_network

# 启动服务
docker-compose -f docker-compose.yml -f docker-compose.shadow-mode.yml up -d
```

## 配置说明

### 1. caby_vision 配置

```bash
# 特征模型配置
FEATURED_MODEL_PATH=/app/models/featured/featured_cat_model_quantized.onnx
FEATURED_REFERENCE_PATH=/app/models/featured/reference_features.json
SHADOW_MODE_ENABLED=true
```

### 2. caby_ai 配置

```bash
# Qdrant配置
QDRANT_HOST=qdrant
QDRANT_PORT=6333

# 影子模式配置
SHADOW_MODE_ENABLED=true
SHADOW_SIMILARITY_THRESHOLD=0.85
SHADOW_NEW_CAT_THRESHOLD=0.70
SHADOW_TOP_K=5
```

### 3. 数据库配置

影子模式会在以下表中添加新字段：

- `record_analysis`: 添加影子模式结果字段
- `shadow_mode_config`: 用户配置表
- `shadow_mode_notifications`: 通知记录表
- `cat_feature_versions`: 特征版本记录表

## 测试验证

### 1. 运行测试脚本

```bash
# 安装测试依赖
pip install requests pillow

# 运行完整测试
python scripts/test-shadow-mode.py --component all --verbose

# 运行特定组件测试
python scripts/test-shadow-mode.py --component vision
python scripts/test-shadow-mode.py --component qdrant
```

### 2. 手动测试

#### 测试caby_vision特征提取API

```bash
curl -X POST http://localhost:8001/featured/extract \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer default_api_key" \
  -d '{
    "image": "base64_encoded_image_data",
    "user_id": "test_user",
    "task": "extract_features"
  }'
```

#### 测试Qdrant连接

```bash
curl http://localhost:6333/
```

### 3. 检查服务状态

```bash
# 查看服务状态
docker-compose -f docker-compose.yml -f docker-compose.shadow-mode.yml ps

# 查看日志
docker-compose -f docker-compose.yml -f docker-compose.shadow-mode.yml logs -f
```

## 监控和维护

### 1. 日志监控

```bash
# 查看caby_vision日志
docker-compose logs -f caby_vision

# 查看caby_ai日志
docker-compose logs -f caby_ai

# 查看Qdrant日志
docker-compose logs -f qdrant
```

### 2. 性能监控

- 监控Qdrant内存使用情况
- 监控特征提取API响应时间
- 监控数据库查询性能

### 3. 数据备份

```bash
# 备份Qdrant数据
docker exec caby_qdrant tar -czf /tmp/qdrant_backup.tar.gz /qdrant/storage

# 备份数据库
mysqldump -h localhost -u root -p cat_toilet_db > shadow_mode_backup.sql
```

## 故障排除

### 1. 常见问题

#### 模型文件未找到
```
错误: Featured model file not found
解决: 检查模型文件路径和权限
```

#### Qdrant连接失败
```
错误: failed to connect to Qdrant
解决: 检查Qdrant服务状态和网络配置
```

#### 特征提取超时
```
错误: feature extraction timeout
解决: 增加超时时间或检查GPU配置
```

### 2. 调试模式

```bash
# 启用调试日志
export LOG_LEVEL=debug

# 重启服务
docker-compose restart caby_ai caby_vision
```

## 升级和回滚

### 1. 升级流程

```bash
# 停止服务
docker-compose -f docker-compose.yml -f docker-compose.shadow-mode.yml down

# 备份数据
./scripts/backup-shadow-mode.sh

# 更新代码和配置
git pull origin main

# 重新部署
./scripts/deploy-shadow-mode.sh
```

### 2. 回滚流程

```bash
# 停止服务
docker-compose -f docker-compose.yml -f docker-compose.shadow-mode.yml down

# 恢复代码
git checkout previous_version

# 恢复数据
./scripts/restore-shadow-mode.sh

# 重新启动
docker-compose -f docker-compose.yml -f docker-compose.shadow-mode.yml up -d
```

## 安全考虑

1. **API密钥管理**: 定期轮换API密钥
2. **网络隔离**: 使用Docker网络隔离服务
3. **数据加密**: 敏感数据传输使用HTTPS
4. **访问控制**: 限制Qdrant和数据库访问权限

## 性能优化

1. **模型优化**: 使用量化模型减少内存占用
2. **缓存策略**: 实现特征向量缓存
3. **批处理**: 支持批量特征提取
4. **资源限制**: 设置合适的Docker资源限制

## 联系支持

如遇到问题，请联系开发团队或查看项目文档。
