# =====================================
# 影子模式部署 Docker Compose 配置
# =====================================
#
# 此文件扩展了现有的Docker Compose配置，添加了影子模式所需的服务
# 使用方法：
# docker-compose -f docker-compose.yml -f docker-compose.shadow-mode.yml up -d
#

version: '3.8'

services:
  # =====================================
  # Qdrant 向量数据库
  # =====================================
  qdrant:
    image: qdrant/qdrant:v1.7.4
    container_name: caby_qdrant
    restart: unless-stopped
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
      - QDRANT__LOG_LEVEL=INFO
    networks:
      - caby_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # =====================================
  # 扩展现有服务配置
  # =====================================
  
  # caby_vision 服务扩展
  caby_vision:
    environment:
      # 影子模式配置
      - FEATURED_MODEL_PATH=/app/models/featured/featured_cat_model_quantized.onnx
      - FEATURED_REFERENCE_PATH=/app/models/featured/reference_features.json
      - SHADOW_MODE_ENABLED=true
    volumes:
      # 添加特征模型文件挂载
      - ./caby_vision/models/featured:/app/models/featured:ro
    depends_on:
      - qdrant

  # caby_ai 服务扩展
  caby_ai:
    environment:
      # Qdrant配置
      - QDRANT_HOST=qdrant
      - QDRANT_PORT=6333
      - QDRANT_API_KEY=
      
      # 影子模式配置
      - SHADOW_MODE_ENABLED=true
      - SHADOW_SIMILARITY_THRESHOLD=0.85
      - SHADOW_NEW_CAT_THRESHOLD=0.70
      - SHADOW_TOP_K=5
      
      # Vision服务配置
      - VISION_HOST=caby_vision
      - VISION_API_KEY=default_api_key
    depends_on:
      - qdrant
      - caby_vision

  # backend_server 服务扩展
  backend_server:
    environment:
      # 影子模式通知配置
      - SHADOW_MODE_NOTIFICATIONS=true
    depends_on:
      - qdrant

# =====================================
# 网络配置
# =====================================
networks:
  caby_network:
    external: true

# =====================================
# 数据卷配置
# =====================================
volumes:
  qdrant_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/qdrant

# =====================================
# 健康检查和监控
# =====================================
x-healthcheck-defaults: &healthcheck-defaults
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 40s
