#!/usr/bin/env python3
"""
影子模式测试脚本
================

此脚本用于测试影子模式的各个组件和功能
包括API端点测试、数据库连接测试、Qdrant集成测试等

使用方法:
python scripts/test-shadow-mode.py [--component COMPONENT] [--verbose]

参数:
--component: 指定测试组件 (all|vision|ai|qdrant|notification)
--verbose: 详细输出
"""

import argparse
import base64
import json
import logging
import requests
import sys
import time
from typing import Dict, List, Optional, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ShadowModeTest:
    """影子模式测试类"""
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        if verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        
        # 服务配置
        self.services = {
            'caby_vision': 'http://localhost:8001',
            'caby_ai': 'http://localhost:8765',
            'qdrant': 'http://localhost:6333',
            'backend': 'http://localhost:8080'
        }
        
        # API密钥
        self.api_keys = {
            'caby_vision': 'default_api_key',
            'caby_ai': 'caby-token',
            'backend': 'your_backend_service_token'
        }
        
        # 测试数据
        self.test_image_base64 = self._generate_test_image()
        self.test_user_id = "test_user_001"
        
    def _generate_test_image(self) -> str:
        """生成测试用的base64图像"""
        # 创建一个简单的测试图像（1x1像素的PNG）
        import io
        from PIL import Image
        
        # 创建一个224x224的RGB图像
        img = Image.new('RGB', (224, 224), color='red')
        
        # 转换为base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_bytes = buffer.getvalue()
        
        return base64.b64encode(img_bytes).decode('utf-8')
    
    def test_service_health(self, service_name: str) -> bool:
        """测试服务健康状态"""
        logger.info(f"测试 {service_name} 服务健康状态...")
        
        try:
            url = f"{self.services[service_name]}/health"
            if service_name == 'qdrant':
                url = f"{self.services[service_name]}/"
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                logger.info(f"✅ {service_name} 服务正常")
                return True
            else:
                logger.error(f"❌ {service_name} 服务异常: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ {service_name} 服务连接失败: {e}")
            return False
    
    def test_vision_featured_api(self) -> bool:
        """测试caby_vision的特征提取API"""
        logger.info("测试caby_vision特征提取API...")
        
        try:
            url = f"{self.services['caby_vision']}/featured/extract"
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f"Bearer {self.api_keys['caby_vision']}"
            }
            
            payload = {
                'image': self.test_image_base64,
                'user_id': self.test_user_id,
                'task': 'extract_features'
            }
            
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success') and 'features' in result:
                    logger.info(f"✅ 特征提取成功，特征维度: {len(result['features'])}")
                    return True
                else:
                    logger.error(f"❌ 特征提取失败: {result}")
                    return False
            else:
                logger.error(f"❌ API调用失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 特征提取测试失败: {e}")
            return False
    
    def test_qdrant_operations(self) -> bool:
        """测试Qdrant向量数据库操作"""
        logger.info("测试Qdrant向量数据库操作...")
        
        try:
            # 测试集合创建
            collection_name = f"test_collection_{int(time.time())}"
            
            # 创建集合
            create_url = f"{self.services['qdrant']}/collections/{collection_name}"
            create_payload = {
                "vectors": {
                    "size": 768,
                    "distance": "Cosine"
                }
            }
            
            response = requests.put(create_url, json=create_payload, timeout=10)
            if response.status_code not in [200, 409]:  # 409 = already exists
                logger.error(f"❌ 创建集合失败: {response.status_code} - {response.text}")
                return False
            
            logger.info("✅ 集合创建成功")
            
            # 测试向量插入
            import random
            test_vector = [random.random() for _ in range(768)]
            
            upsert_url = f"{self.services['qdrant']}/collections/{collection_name}/points"
            upsert_payload = {
                "points": [{
                    "id": "test_point_1",
                    "vector": test_vector,
                    "payload": {
                        "cat_id": "test_cat_001",
                        "cat_name": "测试猫咪",
                        "user_id": self.test_user_id
                    }
                }]
            }
            
            response = requests.put(upsert_url, json=upsert_payload, timeout=10)
            if response.status_code != 200:
                logger.error(f"❌ 向量插入失败: {response.status_code} - {response.text}")
                return False
            
            logger.info("✅ 向量插入成功")
            
            # 测试向量搜索
            search_url = f"{self.services['qdrant']}/collections/{collection_name}/points/search"
            search_payload = {
                "vector": test_vector,
                "limit": 5,
                "with_payload": True
            }
            
            response = requests.post(search_url, json=search_payload, timeout=10)
            if response.status_code != 200:
                logger.error(f"❌ 向量搜索失败: {response.status_code} - {response.text}")
                return False
            
            search_result = response.json()
            if search_result.get('result') and len(search_result['result']) > 0:
                logger.info("✅ 向量搜索成功")
            else:
                logger.error("❌ 向量搜索结果为空")
                return False
            
            # 清理测试集合
            delete_url = f"{self.services['qdrant']}/collections/{collection_name}"
            requests.delete(delete_url, timeout=10)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Qdrant测试失败: {e}")
            return False
    
    def test_shadow_mode_workflow(self) -> bool:
        """测试完整的影子模式工作流程"""
        logger.info("测试完整的影子模式工作流程...")
        
        try:
            # 1. 首先测试特征提取
            if not self.test_vision_featured_api():
                logger.error("❌ 特征提取测试失败，无法继续工作流程测试")
                return False
            
            # 2. 模拟caby_ai的影子模式处理
            # 这里应该调用caby_ai的API，但由于我们没有完整的端点，先跳过
            logger.info("⚠️  caby_ai影子模式API测试跳过（需要完整实现）")
            
            # 3. 测试通知系统
            logger.info("⚠️  通知系统测试跳过（需要backend_server支持）")
            
            logger.info("✅ 影子模式工作流程基础测试完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 影子模式工作流程测试失败: {e}")
            return False
    
    def test_performance(self) -> bool:
        """测试性能指标"""
        logger.info("测试性能指标...")
        
        try:
            # 测试特征提取性能
            start_time = time.time()
            
            for i in range(5):  # 测试5次
                success = self.test_vision_featured_api()
                if not success:
                    logger.error(f"❌ 第{i+1}次特征提取失败")
                    return False
            
            end_time = time.time()
            avg_time = (end_time - start_time) / 5
            
            logger.info(f"✅ 特征提取平均耗时: {avg_time:.2f}秒")
            
            if avg_time > 10:  # 如果平均耗时超过10秒
                logger.warning("⚠️  特征提取耗时较长，可能影响用户体验")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 性能测试失败: {e}")
            return False
    
    def run_component_test(self, component: str) -> bool:
        """运行指定组件的测试"""
        logger.info(f"开始测试组件: {component}")
        
        if component == 'vision':
            return (self.test_service_health('caby_vision') and 
                   self.test_vision_featured_api())
        
        elif component == 'qdrant':
            return (self.test_service_health('qdrant') and 
                   self.test_qdrant_operations())
        
        elif component == 'ai':
            return self.test_service_health('caby_ai')
        
        elif component == 'notification':
            return self.test_service_health('backend')
        
        elif component == 'all':
            results = []
            
            # 健康检查
            for service in ['caby_vision', 'caby_ai', 'qdrant']:
                results.append(self.test_service_health(service))
            
            # 功能测试
            results.append(self.test_vision_featured_api())
            results.append(self.test_qdrant_operations())
            results.append(self.test_shadow_mode_workflow())
            results.append(self.test_performance())
            
            return all(results)
        
        else:
            logger.error(f"❌ 未知组件: {component}")
            return False

def main():
    parser = argparse.ArgumentParser(description='影子模式测试脚本')
    parser.add_argument('--component', default='all', 
                       choices=['all', 'vision', 'ai', 'qdrant', 'notification'],
                       help='指定测试组件')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 创建测试实例
    tester = ShadowModeTest(verbose=args.verbose)
    
    # 运行测试
    logger.info("=" * 50)
    logger.info("开始影子模式测试")
    logger.info("=" * 50)
    
    success = tester.run_component_test(args.component)
    
    logger.info("=" * 50)
    if success:
        logger.info("🎉 所有测试通过！")
        sys.exit(0)
    else:
        logger.error("💥 测试失败！")
        sys.exit(1)

if __name__ == '__main__':
    main()
