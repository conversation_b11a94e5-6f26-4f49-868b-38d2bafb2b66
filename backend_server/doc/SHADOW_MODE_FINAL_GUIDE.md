# 影子模式最终部署指南

## 🎯 概述

影子模式是一个基于特征向量的猫咪识别系统，与现有识别系统并行运行，提供更准确的识别结果和新猫咪发现功能。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   backend_server │    │     caby_ai     │    │   caby_vision   │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 原始识别API │ │◄───┤ │ 双API调用   │ │◄───┤ │ 原始模型    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │        │        │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 影子模式存储│ │◄───┤ │ 相似度比较  │ │◄───┤ │ 特征模型    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │        │        │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    └─────────────────┘
│ │ 通知系统    │ │◄───┤ │   Qdrant    │ │
│ └─────────────┘ │    │ │ 向量数据库  │ │
└─────────────────┘    │ └─────────────┘ │
                       └─────────────────┘
```

### 核心设计原则

1. **职责分离**:
   - caby_ai: 负责视频分析和向量比较，只访问Qdrant
   - backend_server: 负责业务逻辑、猫档案管理、通知管理，只访问MySQL
2. **API通信**: 服务间只通过API通信，避免直接数据库访问
3. **非侵入性**: 与现有系统并行运行，不影响原有功能
4. **用户隔离**: 每个用户独立的特征向量空间

## 🚀 快速部署

### 环境要求
- Docker >= 20.10
- Docker Compose >= 1.29
- MySQL >= 8.0
- Python >= 3.8 (用于测试)

### 一键部署
```bash
# 进入服务器目录
cd /path/to/caby_server

# 运行自动化部署脚本
./backend_server/scripts/deploy-shadow-mode.sh

# 运行测试验证
python caby_vision/scripts/test-shadow-mode.py --component all
```

## 📁 文件结构

```
backend_server/
├── scripts/deploy-shadow-mode.sh              # 主部署脚本
├── database/migrations/add_shadow_mode_columns.sql  # 数据库迁移
├── pkg/shadow/service.go                      # 影子模式业务逻辑服务
├── pkg/shadow/handler.go                      # 影子模式API处理器
├── api/router_shadow.go                       # 影子模式路由
└── doc/SHADOW_MODE_FINAL_GUIDE.md            # 本文档

caby_ai/
├── docker-compose.yml                         # 包含Qdrant配置
├── pkg/shadow/service.go                      # 影子模式向量分析服务
├── pkg/analysis/service.go                    # 分析服务(含API调用backend)
├── pkg/vision/featured_client.go              # 特征提取客户端
└── .env.example                               # 环境变量模板

caby_vision/
├── scripts/test-shadow-mode.py                # 测试脚本
├── triton_service/featured_cat_recognizer.py  # 特征识别器
├── triton_service/model_repository/featured_cat_recognition/  # Triton模型
└── models/featured/                           # 模型文件目录
```

## ⚙️ 配置说明

### caby_ai 环境变量
```bash
# Qdrant配置
QDRANT_HOST=qdrant
QDRANT_PORT=6333
QDRANT_API_KEY=

# 影子模式配置
SHADOW_MODE_ENABLED=true
SHADOW_SIMILARITY_THRESHOLD=0.85
SHADOW_NEW_CAT_THRESHOLD=0.70
SHADOW_TOP_K=5

# Vision服务配置
VISION_HOST=caby_vision
VISION_API_KEY=default_api_key
```

### caby_vision 环境变量
```bash
# 特征模型配置
FEATURED_MODEL_PATH=/app/models/featured/featured_cat_model_quantized.onnx
FEATURED_REFERENCE_PATH=/app/models/featured/reference_features.json
SHADOW_MODE_ENABLED=true
```

## 🔄 工作流程

1. **用户上传视频** → backend_server
2. **视频分析请求** → caby_ai
3. **caby_ai处理**:
   - 双API并行调用: 原始识别API + 影子模式API
   - 特征向量比较 (访问Qdrant)
   - 结果判断: 相似度 > 0.85 → 已知猫咪, < 0.70 → 新猫咪
4. **结果传递** → caby_ai通过API发送结果给backend_server
5. **backend_server处理**:
   - 新猫咪: 创建猫咪档案 (访问MySQL)
   - 更新分析结果
   - 生成通知: "小白（小花）"格式消息
6. **用户通知** → backend_server推送通知

## 🗄️ 数据库变更

### 新增表结构
```sql
-- 影子模式配置表
CREATE TABLE shadow_mode_config (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(20) NOT NULL,
  similarity_threshold DECIMAL(5,4) DEFAULT 0.8500,
  new_cat_threshold DECIMAL(5,4) DEFAULT 0.7000,
  enabled BOOLEAN DEFAULT TRUE,
  notification_enabled BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_user_config (user_id),
  FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- 影子模式通知记录表
CREATE TABLE shadow_mode_notifications (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(20) NOT NULL,
  video_id VARCHAR(64) NOT NULL,
  notification_type ENUM('new_cat', 'different_result', 'low_confidence') NOT NULL,
  original_result VARCHAR(64),
  shadow_result VARCHAR(64),
  similarity_score DECIMAL(5,4),
  message_title VARCHAR(255),
  message_content TEXT,
  sent_at TIMESTAMP NULL,
  read_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_created (user_id, created_at),
  FOREIGN KEY (user_id) REFERENCES users(user_id)
);
```

### record_analysis表扩展
```sql
ALTER TABLE record_analysis 
ADD COLUMN shadow_mode_result TEXT,
ADD COLUMN shadow_similarity DECIMAL(5,4),
ADD COLUMN shadow_matched_cat_id VARCHAR(32),
ADD COLUMN shadow_is_new_cat BOOLEAN DEFAULT FALSE,
ADD COLUMN shadow_confidence DECIMAL(5,4),
ADD COLUMN shadow_model_version VARCHAR(32);
```

## 🧪 测试验证

### API测试
```bash
# 测试特征提取API
curl -X POST http://localhost:8001/featured/extract \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer default_api_key" \
  -d '{
    "image": "base64_encoded_image",
    "user_id": "test_user",
    "task": "extract_features"
  }'
```

### 服务健康检查
```bash
# 检查Qdrant (在caby_ai中)
curl http://localhost:6333/

# 检查caby_vision
curl http://localhost:8001/health

# 运行完整测试
python caby_vision/scripts/test-shadow-mode.py --verbose
```

## 🔧 手动部署步骤

如果需要手动部署：

```bash
# 1. 复制模型文件
mkdir -p caby_vision/models/featured
cp ../caby_training/reid/infrared_cat_recognition_project/deployment/infrared_cat_model_quantized.onnx \
   caby_vision/models/featured/featured_cat_model_quantized.onnx
cp ../caby_training/reid/infrared_cat_recognition_project/deployment/reference_features.json \
   caby_vision/models/featured/reference_features.json

# 2. 执行数据库迁移
mysql -u root -p cat_toilet_db < backend_server/database/migrations/add_shadow_mode_columns.sql

# 3. 启动服务 (按顺序)
cd caby_ai && docker-compose up -d      # 先启动Qdrant
cd ../caby_vision && docker-compose up -d
cd ../backend_server && docker-compose up -d
```

## 📊 性能指标

- **特征提取时间**: < 3秒 (GPU模式)
- **向量搜索时间**: < 100ms
- **内存占用**: 量化模型减少50%内存使用
- **并发支持**: 支持多用户同时使用

## 🛠️ 故障排除

### 常见问题

1. **Qdrant连接失败**
   ```bash
   # 检查caby_ai中的Qdrant服务
   cd caby_ai && docker-compose logs qdrant
   ```

2. **特征提取超时**
   ```bash
   # 检查caby_vision日志
   cd caby_vision && docker-compose logs caby_vision
   ```

3. **模型文件未找到**
   ```bash
   # 确认模型文件存在
   ls -la caby_vision/models/featured/
   ```

## 🔄 升级和维护

### 升级流程
```bash
# 停止所有服务
cd caby_ai && docker-compose down
cd ../caby_vision && docker-compose down
cd ../backend_server && docker-compose down

# 更新代码
git pull origin main

# 重新部署
./backend_server/scripts/deploy-shadow-mode.sh
```

### 数据备份
```bash
# 备份Qdrant数据
docker exec caby_qdrant tar -czf /tmp/qdrant_backup.tar.gz /qdrant/storage

# 备份数据库
mysqldump -u root -p cat_toilet_db > shadow_mode_backup.sql
```

## 🎉 部署完成

影子模式现在已经完全集成到系统中，提供：

✅ **智能猫咪识别**: 基于特征向量的高精度识别  
✅ **新猫咪发现**: 自动检测和命名新猫咪  
✅ **实时通知**: "小白（小花）"格式的结果比较通知  
✅ **用户隔离**: 每用户独立的特征空间  
✅ **渐进式替换**: 支持逐步迁移到新模型  

系统现在可以为用户提供更智能、更准确的猫咪识别服务！🐱✨
