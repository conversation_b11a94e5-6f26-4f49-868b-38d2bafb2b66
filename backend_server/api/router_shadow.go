package api

import (
	"cabycare-server/pkg/shadow"

	"github.com/gin-gonic/gin"
)

// RegisterShadowModeServiceRoutes 注册影子模式服务路由
func RegisterShadowModeServiceRoutes(rg *gin.RouterGroup, shadowHandler *shadow.Handler) {
	shadowGroup := rg.Group("/shadow")
	{
		// 处理影子模式结果 (从caby_ai调用)
		shadowGroup.POST("/result", shadowHandler.ProcessShadowResult)
		
		// 用户配置管理
		shadowGroup.GET("/config/:user_id", shadowHandler.GetUserShadowConfig)
		shadowGroup.PUT("/config/:user_id", shadowHandler.UpdateUserShadowConfig)
		
		// 通知管理
		shadowGroup.GET("/notifications/:user_id", shadowHandler.GetShadowNotifications)
	}
}
