-- =====================================
-- 影子模式数据库迁移脚本
-- =====================================
-- 
-- 此脚本为record_analysis表添加影子模式相关列
-- 用于存储影子模式识别结果和比较数据
--
-- 执行方法：
-- mysql -u username -p database_name < add_shadow_mode_columns.sql
--

USE cat_toilet_db;

-- 为record_analysis表添加影子模式相关列
ALTER TABLE record_analysis 
ADD COLUMN shadow_mode_result TEXT COMMENT '影子模式识别结果JSON',
ADD COLUMN shadow_similarity DECIMAL(5,4) DEFAULT NULL COMMENT '影子模式相似度分数',
ADD COLUMN shadow_matched_cat_id VARCHAR(32) DEFAULT NULL COMMENT '影子模式匹配的猫咪ID',
ADD COLUMN shadow_is_new_cat BOOLEAN DEFAULT FALSE COMMENT '影子模式是否识别为新猫',
ADD COLUMN shadow_confidence DECIMAL(5,4) DEFAULT NULL COMMENT '影子模式置信度',
ADD COLUMN shadow_features_stored BOOLEAN DEFAULT FALSE COMMENT '特征向量是否已存储到Qdrant',
ADD COLUMN shadow_model_version VARCHAR(32) DEFAULT NULL COMMENT '影子模式模型版本';

-- 创建影子模式配置表
CREATE TABLE IF NOT EXISTS shadow_mode_config (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(20) NOT NULL COMMENT '用户ID',
  similarity_threshold DECIMAL(5,4) DEFAULT 0.8500 COMMENT '相似度阈值',
  new_cat_threshold DECIMAL(5,4) DEFAULT 0.7000 COMMENT '新猫阈值',
  enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用影子模式',
  notification_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用通知',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_user_config (user_id),
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='影子模式用户配置表';

-- 创建影子模式通知记录表
CREATE TABLE IF NOT EXISTS shadow_mode_notifications (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(20) NOT NULL COMMENT '用户ID',
  video_id VARCHAR(64) NOT NULL COMMENT '视频ID',
  notification_type ENUM('new_cat', 'different_result', 'low_confidence') NOT NULL COMMENT '通知类型',
  original_result VARCHAR(64) COMMENT '原始识别结果',
  shadow_result VARCHAR(64) COMMENT '影子模式识别结果',
  similarity_score DECIMAL(5,4) COMMENT '相似度分数',
  message_title VARCHAR(255) COMMENT '通知标题',
  message_content TEXT COMMENT '通知内容',
  sent_at TIMESTAMP NULL COMMENT '发送时间',
  read_at TIMESTAMP NULL COMMENT '阅读时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_created (user_id, created_at),
  INDEX idx_video_id (video_id),
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
  FOREIGN KEY (video_id) REFERENCES record_shit(video_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='影子模式通知记录表';

-- 创建猫咪特征版本记录表
CREATE TABLE IF NOT EXISTS cat_feature_versions (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  cat_id VARCHAR(32) NOT NULL COMMENT '猫咪ID',
  user_id VARCHAR(20) NOT NULL COMMENT '用户ID',
  feature_version VARCHAR(32) NOT NULL COMMENT '特征版本',
  model_version VARCHAR(32) NOT NULL COMMENT '模型版本',
  feature_dim INT NOT NULL COMMENT '特征维度',
  qdrant_collection VARCHAR(64) NOT NULL COMMENT 'Qdrant集合名称',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_cat_user (cat_id, user_id),
  INDEX idx_user_version (user_id, feature_version),
  FOREIGN KEY (cat_id) REFERENCES cats(cat_id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='猫咪特征版本记录表';

-- 为新增列添加索引以提高查询性能
ALTER TABLE record_analysis 
ADD INDEX idx_shadow_matched_cat (shadow_matched_cat_id),
ADD INDEX idx_shadow_is_new_cat (shadow_is_new_cat),
ADD INDEX idx_shadow_similarity (shadow_similarity);

-- 插入默认的影子模式配置（为现有用户）
INSERT IGNORE INTO shadow_mode_config (user_id, similarity_threshold, new_cat_threshold, enabled, notification_enabled)
SELECT user_id, 0.8500, 0.7000, TRUE, TRUE 
FROM users 
WHERE status = 1;

-- 创建视图以便于查询影子模式结果
CREATE OR REPLACE VIEW shadow_mode_analysis_view AS
SELECT 
    ra.video_id,
    ra.animal_id,
    ra.cat_confidence,
    ra.shadow_mode_result,
    ra.shadow_similarity,
    ra.shadow_matched_cat_id,
    ra.shadow_is_new_cat,
    ra.shadow_confidence,
    ra.shadow_model_version,
    rs.device_id,
    rs.start_time,
    rs.end_time,
    d.user_id,
    c.name as cat_name,
    sc.name as shadow_matched_cat_name
FROM record_analysis ra
JOIN record_shit rs ON ra.video_id = rs.video_id
JOIN devices d ON rs.device_id = d.device_id
LEFT JOIN cats c ON ra.animal_id = c.cat_id
LEFT JOIN cats sc ON ra.shadow_matched_cat_id = sc.cat_id
WHERE ra.shadow_mode_result IS NOT NULL;

-- 添加注释说明
ALTER TABLE record_analysis COMMENT = '视频分析结果表 - 包含原始识别和影子模式识别结果';
ALTER TABLE shadow_mode_config COMMENT = '影子模式用户配置表 - 存储每个用户的影子模式设置';
ALTER TABLE shadow_mode_notifications COMMENT = '影子模式通知记录表 - 记录影子模式触发的通知';
ALTER TABLE cat_feature_versions COMMENT = '猫咪特征版本记录表 - 跟踪存储在Qdrant中的特征版本';

-- 显示迁移完成信息
SELECT 'Shadow mode database migration completed successfully!' as status;
