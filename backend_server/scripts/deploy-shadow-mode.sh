#!/bin/bash

# =====================================
# 影子模式部署脚本
# =====================================
#
# 此脚本用于部署影子模式到生产环境
# 包括模型文件复制、数据库迁移、服务启动等步骤
#
# 使用方法：
# ./scripts/deploy-shadow-mode.sh [environment]
# 
# 参数：
# environment: dev|staging|prod (默认: dev)
#

set -e  # 遇到错误立即退出

# =====================================
# 配置变量
# =====================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_ROOT="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$BACKEND_ROOT")"
ENVIRONMENT="${1:-dev}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# =====================================
# 环境检查
# =====================================

check_requirements() {
    log_info "检查部署环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装或不在PATH中"
        exit 1
    fi
    
    # 检查MySQL客户端（用于数据库迁移）
    if ! command -v mysql &> /dev/null; then
        log_warning "MySQL客户端未安装，将跳过数据库迁移"
    fi
    
    log_success "环境检查完成"
}

# =====================================
# 模型文件处理
# =====================================

copy_model_files() {
    log_info "复制模型文件..."

    # 创建目标目录
    mkdir -p "$PROJECT_ROOT/caby_vision/models/featured"

    # 检查源模型文件是否存在
    SOURCE_MODEL="$PROJECT_ROOT/../caby_training/reid/infrared_cat_recognition_project/deployment/infrared_cat_model_quantized.onnx"
    SOURCE_FEATURES="$PROJECT_ROOT/../caby_training/reid/infrared_cat_recognition_project/deployment/reference_features.json"

    if [[ -f "$SOURCE_MODEL" ]]; then
        cp "$SOURCE_MODEL" "$PROJECT_ROOT/caby_vision/models/featured/featured_cat_model_quantized.onnx"
        log_success "模型文件复制完成"
    else
        log_error "模型文件不存在: $SOURCE_MODEL"
        log_info "请确保已完成模型训练并生成了量化模型文件"
        exit 1
    fi

    if [[ -f "$SOURCE_FEATURES" ]]; then
        cp "$SOURCE_FEATURES" "$PROJECT_ROOT/caby_vision/models/featured/reference_features.json"
        log_success "参考特征文件复制完成"
    else
        log_warning "参考特征文件不存在: $SOURCE_FEATURES"
        log_info "将使用空的参考特征，影子模式将仅进行特征提取"
    fi
}

# =====================================
# 数据库迁移
# =====================================

run_database_migration() {
    log_info "执行数据库迁移..."
    
    if ! command -v mysql &> /dev/null; then
        log_warning "跳过数据库迁移（MySQL客户端未安装）"
        return
    fi
    
    # 读取数据库配置
    DB_HOST="${DB_HOST:-localhost}"
    DB_PORT="${DB_PORT:-3306}"
    DB_NAME="${DB_NAME:-cat_toilet_db}"
    DB_USER="${DB_USER:-root}"
    
    # 提示输入数据库密码
    echo -n "请输入数据库密码: "
    read -s DB_PASSWORD
    echo
    
    # 执行迁移脚本
    MIGRATION_FILE="$BACKEND_ROOT/database/migrations/add_shadow_mode_columns.sql"
    
    if [[ -f "$MIGRATION_FILE" ]]; then
        mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$MIGRATION_FILE"
        log_success "数据库迁移完成"
    else
        log_error "迁移文件不存在: $MIGRATION_FILE"
        exit 1
    fi
}

# =====================================
# 配置文件生成
# =====================================

generate_config_files() {
    log_info "生成配置文件..."

    # 生成caby_ai环境变量文件
    if [[ ! -f "$PROJECT_ROOT/caby_ai/.env" ]]; then
        cp "$PROJECT_ROOT/caby_ai/.env.example" "$PROJECT_ROOT/caby_ai/.env"
        log_info "已创建caby_ai/.env文件，请根据需要修改配置"
    fi

    # 生成caby_vision环境变量文件
    if [[ ! -f "$PROJECT_ROOT/caby_vision/.env" ]]; then
        cp "$PROJECT_ROOT/caby_vision/env.example" "$PROJECT_ROOT/caby_vision/.env"
        log_info "已创建caby_vision/.env文件，请根据需要修改配置"
    fi

    log_success "配置文件生成完成"
}

# =====================================
# 服务部署
# =====================================

deploy_services() {
    log_info "部署影子模式服务..."

    # 创建网络（如果不存在）
    docker network create caby_network 2>/dev/null || true

    # 首先部署caby_ai (包含Qdrant)
    log_info "部署caby_ai (包含Qdrant向量数据库)..."
    cd "$PROJECT_ROOT/caby_ai"
    docker-compose down 2>/dev/null || true
    docker-compose up -d --build

    # 等待Qdrant启动
    log_info "等待Qdrant启动..."
    sleep 20

    # 部署caby_vision
    log_info "部署caby_vision..."
    cd "$PROJECT_ROOT/caby_vision"
    docker-compose down 2>/dev/null || true
    docker-compose up -d --build

    # 部署backend_server
    log_info "部署backend_server..."
    cd "$BACKEND_ROOT"
    docker-compose down 2>/dev/null || true
    docker-compose up -d --build

    # 等待所有服务启动
    log_info "等待所有服务启动..."
    sleep 30

    # 检查服务状态
    check_service_health

    log_success "影子模式服务部署完成"
}

# =====================================
# 健康检查
# =====================================

check_service_health() {
    log_info "检查服务健康状态..."
    
    # 检查Qdrant
    if curl -f http://localhost:6333/health >/dev/null 2>&1; then
        log_success "Qdrant服务正常"
    else
        log_error "Qdrant服务异常"
    fi
    
    # 检查caby_vision
    if curl -f http://localhost:8001/health >/dev/null 2>&1; then
        log_success "caby_vision服务正常"
    else
        log_error "caby_vision服务异常"
    fi
    
    # 检查caby_ai
    if curl -f http://localhost:8765/health >/dev/null 2>&1; then
        log_success "caby_ai服务正常"
    else
        log_error "caby_ai服务异常"
    fi
}

# =====================================
# 主函数
# =====================================

main() {
    log_info "开始部署影子模式 (环境: $ENVIRONMENT)"
    
    check_requirements
    copy_model_files
    generate_config_files
    
    # 询问是否执行数据库迁移
    echo -n "是否执行数据库迁移? (y/N): "
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        run_database_migration
    else
        log_info "跳过数据库迁移"
    fi
    
    deploy_services
    
    log_success "影子模式部署完成！"
    log_info "架构说明："
    log_info "  - Qdrant向量数据库运行在caby_ai服务中"
    log_info "  - caby_ai负责所有向量比较和分析"
    log_info "  - backend_server只接收分析结果"
    log_info ""
    log_info "请检查服务日志确保一切正常运行："
    log_info "  caby_ai (含Qdrant): cd $PROJECT_ROOT/caby_ai && docker-compose logs -f"
    log_info "  caby_vision: cd $PROJECT_ROOT/caby_vision && docker-compose logs -f"
    log_info "  backend_server: cd $BACKEND_ROOT && docker-compose logs -f"
    log_info ""
    log_info "详细文档: $BACKEND_ROOT/doc/SHADOW_MODE_FINAL_GUIDE.md"
}

# 执行主函数
main "$@"
