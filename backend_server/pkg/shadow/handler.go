package shadow

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Handler 影子模式API处理器
type Handler struct {
	service *Service
}

// NewHandler 创建新的处理器
func NewHandler(db *gorm.DB) *Handler {
	return &Handler{
		service: NewService(db),
	}
}

// ProcessShadowResultRequest 处理影子模式结果请求
type ProcessShadowResultRequest struct {
	UserID       string            `json:"user_id" binding:"required"`
	VideoID      string            `json:"video_id" binding:"required"`
	ShadowResult *ShadowModeResult `json:"shadow_result" binding:"required"`
}

// ProcessShadowResultResponse 处理影子模式结果响应
type ProcessShadowResultResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	CatID   string `json:"cat_id,omitempty"`
}

// ProcessShadowResult 处理影子模式结果
func (h *Handler) ProcessShadowResult(c *gin.Context) {
	var req ProcessShadowResultRequest
	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 处理影子模式结果
	err := h.service.ProcessShadowModeResult(c.Request.Context(), req.UserID, req.VideoID, req.ShadowResult)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to process shadow mode result",
			"details": err.Error(),
		})
		return
	}

	// 返回成功响应
	response := ProcessShadowResultResponse{
		Success: true,
		Message: "Shadow mode result processed successfully",
	}

	// 如果是新猫咪，返回猫咪ID
	if req.ShadowResult.IsNewCat {
		response.CatID = req.ShadowResult.MatchedCatID
	}

	c.JSON(http.StatusOK, response)
}

// GetUserShadowConfig 获取用户影子模式配置
func (h *Handler) GetUserShadowConfig(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "User ID is required",
		})
		return
	}

	config, err := h.service.GetUserShadowConfig(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get user shadow config",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"config":  config,
	})
}

// UpdateUserShadowConfigRequest 更新用户影子模式配置请求
type UpdateUserShadowConfigRequest struct {
	SimilarityThreshold  *float64 `json:"similarity_threshold,omitempty"`
	NewCatThreshold      *float64 `json:"new_cat_threshold,omitempty"`
	Enabled              *bool    `json:"enabled,omitempty"`
	NotificationEnabled  *bool    `json:"notification_enabled,omitempty"`
}

// UpdateUserShadowConfig 更新用户影子模式配置
func (h *Handler) UpdateUserShadowConfig(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "User ID is required",
		})
		return
	}

	var req UpdateUserShadowConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 获取现有配置
	config, err := h.service.GetUserShadowConfig(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get user shadow config",
			"details": err.Error(),
		})
		return
	}

	// 更新配置
	if req.SimilarityThreshold != nil {
		config.SimilarityThreshold = *req.SimilarityThreshold
	}
	if req.NewCatThreshold != nil {
		config.NewCatThreshold = *req.NewCatThreshold
	}
	if req.Enabled != nil {
		config.Enabled = *req.Enabled
	}
	if req.NotificationEnabled != nil {
		config.NotificationEnabled = *req.NotificationEnabled
	}

	// 保存配置
	err = h.service.db.Save(config).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to update user shadow config",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Shadow mode config updated successfully",
		"config":  config,
	})
}

// GetShadowNotifications 获取影子模式通知
func (h *Handler) GetShadowNotifications(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "User ID is required",
		})
		return
	}

	// 分页参数
	page := c.DefaultQuery("page", "1")
	limit := c.DefaultQuery("limit", "20")

	// TODO: 实现分页查询通知
	// 这里可以添加具体的查询逻辑

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Feature not implemented yet",
		"page":    page,
		"limit":   limit,
	})
}
