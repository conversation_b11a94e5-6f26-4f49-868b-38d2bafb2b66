package shadow

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"backend-server/pkg/cattoilet"
	"gorm.io/gorm"
)

// Service 影子模式业务逻辑服务
type Service struct {
	db *gorm.DB
}

// ShadowModeResult 影子模式结果（从caby_ai接收）
type ShadowModeResult struct {
	OriginalResult   string    `json:"original_result"`
	ShadowResult     string    `json:"shadow_result"`
	Similarity       float64   `json:"similarity"`
	IsNewCat        bool      `json:"is_new_cat"`
	MatchedCatID    string    `json:"matched_cat_id"`
	Confidence      float64   `json:"confidence"`
	Features        []float64 `json:"features"`
	FeatureDim      int       `json:"feature_dim"`
	ModelVersion    string    `json:"model_version"`
	Timestamp       string    `json:"timestamp"`
}

// NewService 创建新的影子模式服务
func NewService(db *gorm.DB) *Service {
	return &Service{
		db: db,
	}
}

// ProcessShadowModeResult 处理影子模式结果
func (s *Service) ProcessShadowModeResult(ctx context.Context, userID, videoID string, shadowResult *ShadowModeResult) error {
	// 1. 如果是新猫咪，创建猫咪档案
	var finalCatID string
	if shadowResult.IsNewCat {
		catID, err := s.CreateNewCat(ctx, userID, shadowResult)
		if err != nil {
			log.Printf("Failed to create new cat: %v", err)
			return err
		}
		finalCatID = catID
	} else {
		finalCatID = shadowResult.MatchedCatID
	}

	// 2. 更新分析结果
	err := s.UpdateAnalysisResult(ctx, videoID, shadowResult, finalCatID)
	if err != nil {
		log.Printf("Failed to update analysis result: %v", err)
		return err
	}

	// 3. 处理通知
	err = s.ProcessNotification(ctx, userID, videoID, shadowResult)
	if err != nil {
		log.Printf("Failed to process notification: %v", err)
		// 不返回错误，因为主要逻辑已完成
	}

	return nil
}

// CreateNewCat 创建新猫咪档案
func (s *Service) CreateNewCat(ctx context.Context, userID string, shadowResult *ShadowModeResult) (string, error) {
	// 生成新猫咪ID
	catID := s.generateNewCatID()
	
	// 创建猫咪记录
	cat := &cattoilet.Cat{
		CatID:     catID,
		UserID:    userID,
		Name:      shadowResult.ShadowResult,
		Gender:    0, // 未知
		Birthday:  time.Now(),
		Breed:     "Unknown",
		Color:     "Unknown",
		Weight:    0.0,
		Status:    1, // 活跃
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err := s.db.Create(cat).Error
	if err != nil {
		return "", fmt.Errorf("failed to create cat record: %w", err)
	}

	// 记录特征版本信息
	featureVersion := &cattoilet.CatFeatureVersion{
		CatID:            catID,
		UserID:           userID,
		FeatureVersion:   shadowResult.ModelVersion,
		ModelVersion:     shadowResult.ModelVersion,
		FeatureDim:       shadowResult.FeatureDim,
		QdrantCollection: fmt.Sprintf("cat_features_%s", userID),
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	err = s.db.Create(featureVersion).Error
	if err != nil {
		log.Printf("Failed to create feature version record: %v", err)
		// 不返回错误，因为主要记录已创建
	}

	log.Printf("Created new cat: ID=%s, Name=%s, UserID=%s", catID, shadowResult.ShadowResult, userID)
	return catID, nil
}

// UpdateAnalysisResult 更新分析结果
func (s *Service) UpdateAnalysisResult(ctx context.Context, videoID string, shadowResult *ShadowModeResult, finalCatID string) error {
	// 序列化影子模式结果
	shadowResultJSON, err := json.Marshal(shadowResult)
	if err != nil {
		return fmt.Errorf("failed to marshal shadow result: %w", err)
	}

	// 更新分析结果
	updates := map[string]interface{}{
		"animal_id":              finalCatID,
		"shadow_mode_result":     string(shadowResultJSON),
		"shadow_similarity":      shadowResult.Similarity,
		"shadow_matched_cat_id":  shadowResult.MatchedCatID,
		"shadow_is_new_cat":      shadowResult.IsNewCat,
		"shadow_confidence":      shadowResult.Confidence,
		"shadow_features_stored": true,
		"shadow_model_version":   shadowResult.ModelVersion,
		"updated_at":             time.Now(),
	}

	err = s.db.Model(&cattoilet.RecordAnalysis{}).
		Where("video_id = ?", videoID).
		Updates(updates).Error

	if err != nil {
		return fmt.Errorf("failed to update analysis result: %w", err)
	}

	log.Printf("Updated analysis result for video %s with shadow mode data", videoID)
	return nil
}

// ProcessNotification 处理通知
func (s *Service) ProcessNotification(ctx context.Context, userID, videoID string, shadowResult *ShadowModeResult) error {
	// 检查用户是否启用通知
	config, err := s.GetUserShadowConfig(ctx, userID)
	if err != nil {
		log.Printf("Failed to get user shadow config: %v", err)
		return err
	}

	if !config.NotificationEnabled {
		return nil // 用户未启用通知
	}

	// 判断是否需要发送通知
	shouldNotify, notificationType := s.shouldSendNotification(shadowResult)
	if !shouldNotify {
		return nil
	}

	// 生成通知内容
	title, content := s.generateNotificationContent(shadowResult, notificationType)

	// 创建通知记录
	notification := &cattoilet.ShadowModeNotification{
		UserID:           userID,
		VideoID:          videoID,
		NotificationType: notificationType,
		OriginalResult:   &shadowResult.OriginalResult,
		ShadowResult:     &shadowResult.ShadowResult,
		SimilarityScore:  &shadowResult.Similarity,
		MessageTitle:     &title,
		MessageContent:   &content,
		CreatedAt:        time.Now(),
	}

	err = s.db.Create(notification).Error
	if err != nil {
		return fmt.Errorf("failed to create notification record: %w", err)
	}

	// TODO: 这里可以集成实际的推送服务
	log.Printf("Notification created for user %s: %s", userID, title)

	return nil
}

// GetUserShadowConfig 获取用户影子模式配置
func (s *Service) GetUserShadowConfig(ctx context.Context, userID string) (*cattoilet.ShadowModeConfig, error) {
	var config cattoilet.ShadowModeConfig
	
	err := s.db.Where("user_id = ?", userID).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建默认配置
			config = cattoilet.ShadowModeConfig{
				UserID:              userID,
				SimilarityThreshold: 0.85,
				NewCatThreshold:     0.70,
				Enabled:             true,
				NotificationEnabled: true,
				CreatedAt:           time.Now(),
				UpdatedAt:           time.Now(),
			}
			
			err = s.db.Create(&config).Error
			if err != nil {
				return nil, fmt.Errorf("failed to create default config: %w", err)
			}
		} else {
			return nil, fmt.Errorf("failed to get user config: %w", err)
		}
	}

	return &config, nil
}

// shouldSendNotification 判断是否应该发送通知
func (s *Service) shouldSendNotification(shadowResult *ShadowModeResult) (bool, string) {
	// 检测到新猫咪
	if shadowResult.IsNewCat {
		return true, "new_cat"
	}

	// 影子模式结果与原始结果不一致
	if shadowResult.OriginalResult != shadowResult.ShadowResult {
		return true, "different_result"
	}

	// 置信度较低
	if shadowResult.Confidence < 0.7 {
		return true, "low_confidence"
	}

	return false, ""
}

// generateNotificationContent 生成通知内容
func (s *Service) generateNotificationContent(shadowResult *ShadowModeResult, notificationType string) (string, string) {
	switch notificationType {
	case "new_cat":
		title := fmt.Sprintf("发现新猫咪：%s", shadowResult.ShadowResult)
		content := fmt.Sprintf("系统检测到一只新的猫咪，已自动命名为"%s"。您可以在猫咪管理中修改名称和添加更多信息。", shadowResult.ShadowResult)
		return title, content

	case "different_result":
		// 格式：原始结果（影子模式结果）
		title := fmt.Sprintf("%s（%s）", shadowResult.OriginalResult, shadowResult.ShadowResult)
		content := fmt.Sprintf("影子模式识别结果与原始结果不一致。原始识别：%s，影子模式识别：%s，相似度：%.2f", 
			shadowResult.OriginalResult, shadowResult.ShadowResult, shadowResult.Similarity)
		return title, content

	case "low_confidence":
		title := fmt.Sprintf("识别置信度较低：%s", shadowResult.ShadowResult)
		content := fmt.Sprintf("猫咪识别的置信度较低（%.2f），建议检查图像质量或更新猫咪特征数据。", shadowResult.Confidence)
		return title, content

	default:
		title := fmt.Sprintf("影子模式通知：%s", shadowResult.ShadowResult)
		content := "影子模式检测到异常情况，请查看详细信息。"
		return title, content
	}
}

// generateNewCatID 生成新猫咪ID
func (s *Service) generateNewCatID() string {
	timestamp := time.Now().Format("20060102150405")
	return fmt.Sprintf("NewCat%s", timestamp)
}
